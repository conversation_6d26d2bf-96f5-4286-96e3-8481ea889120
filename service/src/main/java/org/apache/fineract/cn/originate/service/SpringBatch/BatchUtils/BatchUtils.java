package org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import org.apache.fineract.cn.originate.api.v1.validation.ExceptionConstants.DealConstants;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchDomain.BatchStatusDomain;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.StageDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.StageDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityRepository;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.StreamSupport;


//TODO: Remove the codes that are related to the AWS, Since we are refactoring the logic to fetch it from DMS service

@Service
public class BatchUtils {

    private static final String FETCH_QUERY_KEY_FROM_CONFIG_QUERY = "SELECT configuration_details -> ? ->>'update' " +
            " FROM originate_batch_configurations " +
            " WHERE configuration_name = ?";
    private static final String FETCH_QUERY_FILTER_KEY_FROM_CONFIG_QUERY = "SELECT configuration_details -> ? ->'filterChild' " +
            " FROM originate_batch_configurations " +
            " WHERE configuration_name = ?";
    private static final String FETCH_QUERY_PARTITION_KEY_FROM_CONFIG_QUERY = "SELECT configuration_details -> ? ->'partition' " +
            " FROM originate_batch_configurations " +
            " WHERE configuration_name = ?";
    private static final String FETCH_CONFIG_FROM_CONFIG_QUERY = "SELECT configuration_details -> ? " +
            " FROM originate_batch_configurations " +
            " WHERE configuration_name = ?";
    private final String fetchlastNumnber = " SELECT oda.deal_asset_obj ->> ?\n" +
            "FROM public.originate_deal x\n" +
            "JOIN originate_deal_asset oda ON oda.id = x.deal_asset_id\n" +
            "WHERE x.business_process_id = ?\n" +
            "ORDER BY (oda.deal_asset_obj ->> ?)::integer DESC\n" +
            "LIMIT 1;";


    private final JdbcTemplate jdbcTemplate;
    @Autowired
    private BusinessProcessDefinitionRepository businessProcessDefinitionRepository;
    @Autowired
    private StageDefinitionRepository stageDefinitionRepository;
    @Autowired
    private EntityRepository entityRepository;
    private ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Constructor that initializes BatchUtils with a JdbcTemplate for database operations.
     * Sets up the utility class for batch processing operations including configuration
     * retrieval, data transformation, and database queries.
     */
    public BatchUtils(JdbcTemplate jdbcTemplate) {

        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Transforms a flat JSON structure into a nested format with name-value pairs.
     * Converts each field in the root node into an object with 'name' and 'value' properties,
     * organizing them under the specified field name for batch processing compatibility.
     */
    public static ObjectNode transformJson(JsonNode rootNode, ObjectMapper mapper, String fieldName) {
        ObjectNode result = mapper.createObjectNode();
        ArrayNode entityDetail = mapper.createArrayNode();

        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode valueNode = entry.getValue();

            ObjectNode innerObject = mapper.createObjectNode();
            ObjectNode nameValuePair = mapper.createObjectNode();
            nameValuePair.put("name", key);
            if (valueNode.isObject() || valueNode.isArray()) {
                nameValuePair.set("value", valueNode);
            } else {
                nameValuePair.put("value", valueNode.asText());
            }

            innerObject.set(key, nameValuePair);
            entityDetail.add(innerObject);
        }

        result.set(fieldName, entityDetail);
        return result;
    }

//    public InputStream downloadFileFromS3(String s3Url) throws Exception {
//        URL url = new URL(s3Url);
//        String[] pathParts = url.getPath().split("/", 2);
//        if (pathParts.length < 2) {
//            throw new IllegalArgumentException("Invalid S3 URL format. Could not extract bucket and key.");
//        }
//        String bucketName = url.getHost().split("\\.")[0];
//        String fileKey = pathParts[1];
//
//        S3Client s3Client = S3Client.builder()
//                .region(Region.AP_SOUTHEAST_2)
//                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKeyId, secretAccessKey)))
//                .build();
//
//        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
//                .bucket(bucketName)
//                .key(fileKey)
//                .build();
//        ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);
//        return s3Object;
//    }

    /**
     * Removes all comma characters from the input string.
     * Utility method for cleaning CSV data and preparing strings for processing
     * where commas might interfere with parsing or formatting.
     */
    public static String removeCommas(String input) {
        return input.replace(",", "");
    }

    /**
     * Replaces special characters and whitespace with underscores for field name normalization.
     * Converts mathematical operators, brackets, and multiple spaces into single underscores,
     * then removes leading and trailing underscores for clean field names.
     */
    public static String replaceSpecialCharacters(String input) {
        Pattern pattern = Pattern.compile("\\s*([&+\\-*/()\\[\\]{}])\\s*|\\s+");
        String intermediate = pattern.matcher(input).replaceAll("_");
        String result = intermediate.replaceAll("_+", "_");
        result = result.replaceAll("^_+", "").replaceAll("_+$", "");

        return result;
    }

    /**
     * Extracts the filename without extension from a file URL.
     * Parses the URL path to get the filename and removes the file extension,
     * useful for generating identifiers or names based on uploaded files.
     */
    public static String getFileNameWithoutExtension(String fileUrl) throws Exception {
        URL url = new URL(fileUrl);
        String fileName = url.getPath();
        fileName = fileName.substring(fileName.lastIndexOf('/') + 1);
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            fileName = fileName.substring(0, dotIndex);
        }
        return fileName;
    }

    /**
     * Converts the first character of a string to lowercase for camelCase formatting.
     * Ensures consistent field naming conventions by making the first letter lowercase
     * while preserving the rest of the string's case.
     */
    public static String toCamelCase(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.substring(0, 1).toLowerCase() + input.substring(1);
    }

    /**
     * Replaces a field value in the main JSON node with an updated value.
     * Handles empty object cleanup and performs direct value replacement
     * for specific field types during batch merge operations.
     */
    private static void replaceWithUpdatedValue(JsonNode mainNode, JsonNode updatedValue, String updatedFieldName) {
        // TODO: Replace the empty object ({}) returned by the WE response with an empty string.
        //  This is a temporary fix; the actual fix should be implemented in WE.
        updatedValue = checkForEmptyObject(updatedValue);
        ((ObjectNode) mainNode.findValue(updatedFieldName)).replace("value", updatedValue.get("value"));
    }

    /**
     * Checks if a JsonNode is an empty object and converts it to an empty string.
     * Temporary fix to handle empty objects returned by workflow engine responses
     * by converting them to empty text nodes for consistent processing.
     */
    private static JsonNode checkForEmptyObject(JsonNode updatedValue) {
        if (updatedValue.isObject() && updatedValue.size() == 0) { // Check if it's an empty JSON object
            updatedValue = TextNode.valueOf("");
        }
        return updatedValue;
    }

    /**
     * Merges update data into the main JSON node based on business process field definitions.
     * Handles complex merging logic for different input types including arrays, objects,
     * repetitive sections, tables, and various field types with intelligent conflict resolution.
     */
    public JsonNode batchMerge(JsonNode mainNode, JsonNode updateNode, BusinessProcessDefinitionEntity businessProcessDefinitionEntity) {
        Iterator<String> fieldNames = updateNode.fieldNames();
        while (fieldNames.hasNext()) {
            String updatedFieldName = fieldNames.next();
            JsonNode valueToBeUpdated = mainNode.findValue(updatedFieldName);
            JsonNode updatedValue = updateNode.get(updatedFieldName);

            if (valueToBeUpdated != null && valueToBeUpdated.isArray() &&
                    updatedValue.isArray()) {
                if ("value".equals(updatedFieldName) && mainNode instanceof ObjectNode) {

                    updatedValue = checkForEmptyObject(updatedValue);
                    ((ObjectNode) mainNode).replace(updatedFieldName, updatedValue);
                } else {
                    for (int i = 0; i < updatedValue.size(); i++) {
                        JsonNode updatedChildNode = updatedValue.get(i);
                        if (valueToBeUpdated.size() <= i) {
                            ((ArrayNode) valueToBeUpdated).add(updatedChildNode);
                        }
                        JsonNode childNodeToBeUpdated = valueToBeUpdated.get(i);
                        batchMerge(childNodeToBeUpdated, updatedChildNode, businessProcessDefinitionEntity);
                    }
                }
            } else if (valueToBeUpdated != null && valueToBeUpdated.isObject()) {
                String inputType = valueToBeUpdated.has("inputType") ? valueToBeUpdated.get("inputType").asText() : "";
                ArrayList filterValue = this.getfilterValue(businessProcessDefinitionEntity.getName(), "Update_deal_keys");
                if ((inputType.equals("Table") || inputType.equals("Repetitive Section"))) {

                    for (JsonNode fieldNode : mainNode) {
                        if (fieldNode.has(updatedFieldName)) {
                            JsonNode mainArrayNode = fieldNode.get(updatedFieldName).get("value");
                            JsonNode updatedArrayNode = updatedValue.get("value");
                            if (mainArrayNode != null && updatedArrayNode != null && !mainArrayNode.isArray()) {
                                mainArrayNode = JsonNodeFactory.instance.arrayNode();
                            }
                            if (mainArrayNode != null && updatedArrayNode != null &&
                                    mainArrayNode.isArray() && updatedArrayNode.isArray()) {

                                ArrayNode mainArray = (ArrayNode) mainArrayNode;

                                for (JsonNode updatedObj : updatedArrayNode) {
                                    boolean updated = false;

                                    for (int i = 0; i < mainArray.size(); i++) {
                                        JsonNode existingObj = mainArray.get(i);

                                        for (Iterator<String> it = updatedObj.fieldNames(); it.hasNext(); ) {
                                            String fieldName = it.next();

                                            if (fieldName.toLowerCase().equalsIgnoreCase(filterValue.get(0).toString())) {
                                                String updatedId = updatedObj.get(fieldName).asText();
                                                String existingId = existingObj.has(fieldName) ? existingObj.get(fieldName).asText() : null;
                                                if (updatedId.equals(existingId)) {
                                                    mainArray.set(i, updatedObj);
                                                    updated = true;
                                                    break;
                                                }
                                            }
                                        }
                                        if (updated) break;
                                    }
                                    if (!updated) {
                                        mainArray.add(updatedObj);
                                    }
                                }
                                ((ObjectNode) fieldNode.get(updatedFieldName)).set("value", mainArray);
                            }
                            break;
                        }
                    }
                } else if (inputType.equals("formly")) {
                    JsonNode foundNode = updateNode.get(updatedFieldName);
                    if (foundNode != null && foundNode.has("value") && !foundNode.get("value").isNull() && foundNode.get("value").size() != 0) {
                        replaceWithUpdatedValue(mainNode, updatedValue, updatedFieldName);
                    }
                } else if (inputType.equals("Document") || inputType.equals("Generate Document") || inputType.equals("Address") || inputType.equals(
                        "Searchable picklist") || inputType.equals("Advance Picklist") || inputType.equals("Fetch and Map Data")) {
                    replaceWithUpdatedValue(mainNode, updatedValue, updatedFieldName);
                } else {
                    if (valueToBeUpdated.has("value")) {
                        if (updatedValue.isTextual() || updatedValue.isNumber() || updatedValue.isBoolean()) {
                            ((ObjectNode) valueToBeUpdated).put("value", updatedValue.asText());
                        } else if (updatedValue.has("value")) {
                            ((ObjectNode) valueToBeUpdated).replace("value", updatedValue.get("value"));
                        } else {
                            batchMerge(valueToBeUpdated, updatedValue, businessProcessDefinitionEntity);
                        }
                    } else {
                        batchMerge(valueToBeUpdated, updatedValue, businessProcessDefinitionEntity);
                    }
                }
            } else {
                if (mainNode instanceof ObjectNode && ("value".equals(updatedFieldName))) {
                    // TODO: Replace the empty object ({}) returned by the WE response with an empty string.
                    //  This is a temporary fix; the actual fix should be implemented in WE.
                    updatedValue = checkForEmptyObject(updatedValue);
                    ((ObjectNode) mainNode).replace(updatedFieldName, updatedValue);
                }
            }
        }

        return mainNode;
    }

    /**
     * Checks if an Excel row is completely empty by examining all cells.
     * Returns true if all cells in the row are null or contain empty strings,
     * useful for skipping empty rows during Excel file processing.
     */
    public boolean isRowEmpty(Row row) {
        for (Cell cell : row) {
            if (cell != null && !StringUtils.isEmpty(cell.getStringCellValue())) {
                return false;
            }
        }
        return true;
    }

    //    public InputStream downloadFileFromS3ToTemp(String s3Url) throws Exception {
//        URL url = new URL(s3Url);
//        String[] pathParts = url.getPath().split("/", 2);
//        if (pathParts.length < 2) {
//            throw new IllegalArgumentException("Invalid S3 URL format. Could not extract bucket and key.");
//        }
//        String bucketName = url.getHost().split("\\.")[0];
//        String fileKey = pathParts[1];
//        S3Client s3Client = S3Client.builder()
//                .region(Region.AP_SOUTHEAST_2)
//                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKeyId, secretAccessKey)))
//                .build();
//        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
//                .bucket(bucketName)
//                .key(fileKey)
//                .build();
//        ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);
//        Path tempFile = Files.createTempFile("s3_download_", ".tmp");
//        try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile.toFile())) {
//            byte[] buffer = new byte[8192];
//            int bytesRead;
//            while ((bytesRead = s3Object.read(buffer)) != -1) {
//                fileOutputStream.write(buffer, 0, bytesRead);
//            }
//
//        }
//
//        return Files.newInputStream(tempFile);
//    }
//    public InputStream streamFileFromS3(String s3Url) throws Exception {
//        URL url = new URL(s3Url);
//        String[] pathParts = url.getPath().split("/", 2);
//        if (pathParts.length < 2) {
//            throw new IllegalArgumentException("Invalid S3 URL format. Could not extract bucket and key.");
//        }
//        String bucketName = url.getHost().split("\\.")[0];
//        String fileKey = pathParts[1];
//
//        S3Client s3Client = S3Client.builder()
//                .region(Region.AP_SOUTHEAST_2)
//                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKeyId, secretAccessKey)))
//                .build();
//
//        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
//                .bucket(bucketName)
//                .key(fileKey)
//                .build();
//
//        ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);
//        return new BufferedInputStream(s3Object);
//    }
    /**
     * Creates an input stream for reading a local file with validation.
     * Checks if the file exists and is not a directory before creating
     * a buffered input stream for efficient file reading operations.
     */
    public InputStream streamFileFromLocal(String filePath) throws Exception {
        File file = new File(filePath);
        if (!file.exists() || file.isDirectory()) {
            throw new FileNotFoundException("File not found or is a directory: " + filePath);
        }
        return new BufferedInputStream(Files.newInputStream(file.toPath()));
    }

    /**
     * Stores a key-value pair in the job execution context for caching purposes.
     * This allows sharing data across different steps and items within the same batch job,
     * improving performance by avoiding repeated database lookups.
     */
    public void putInJobExecutionContext(String key, Object value) {
        ExecutionContext stepExecutionContext = StepSynchronizationManager.getContext().getStepExecution().getJobExecution().getExecutionContext();
        stepExecutionContext.put(key, value);
    }

    /**
     * Retrieves batch execution status information by job execution ID.
     * Queries the batch execution tables to get step details, timing information,
     * counts, and validation error information for monitoring batch job progress.
     */
    public BatchStatusDomain getStatusById(Long id) {
        String query = "SELECT bse.step_execution_id,\n" +
                "       bse.step_name,\n" +
                "       bse.job_execution_id,\n" +
                "       bse.start_time,\n" +
                "       bse.end_time,\n" +
                "       bse.status,\n" +
                "       bse.read_count,\n" +
                "       bse.filter_count,\n" +
                "       bse.write_count,\n" +
                "       bse.exit_code,\n" +
                "       bse.exit_message,\n" +
                "       bse.last_updated,\n" +
                "       COALESCE(\n" +
                "           (SELECT regexp_matches(sec.short_context, 'validationErrorCount[^:]*:(\\\\d+)', 'g'))[1]::int,\n" +
                "           0\n" +
                "       ) AS validation_error_count\n" +
                "FROM public.batch_step_execution bse\n" +
                "LEFT JOIN public.batch_step_execution_context sec\n" +
                "       ON bse.step_execution_id = sec.step_execution_id\n" +
                "WHERE bse.job_execution_id = ?\n" +
                "  AND (bse.step_name = 'deal_process_step' OR bse.step_name = 'entity_csv_process_step')\n" +
                "LIMIT 1;\n";

        try {
            return this.jdbcTemplate.queryForObject(query, new BatchStatusRowMapper(), id);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * Finds the first stage definition for a given business process ID.
     * Retrieves the business process and its associated stages, returning the second stage
     * (index 1) as the first operational stage, skipping the initial setup stage.
     */
    public Optional<StageDefinitionEntity> findFirstStageDefinition(Long businessProcessId) {
        Optional<StageDefinitionEntity> firstStageDefinitionEntity = Optional.empty();
        Optional<BusinessProcessDefinitionEntity> businessProcessDefinitionEntity = this.businessProcessDefinitionRepository
                .findById(businessProcessId);
        if (businessProcessDefinitionEntity.isPresent()) {
            final List<StageDefinitionEntity> savedStageDefinitionEntity = this.stageDefinitionRepository
                    .findByBusinessProcessDefinitionEntity(businessProcessDefinitionEntity.get(),
                            new Sort(Sort.Direction.ASC, DealConstants.ORDER));
            if (!savedStageDefinitionEntity.isEmpty() && savedStageDefinitionEntity.size() > 1) {
                firstStageDefinitionEntity = Optional.ofNullable(savedStageDefinitionEntity.get(1));
            }
        }
        return firstStageDefinitionEntity;
    }

    /**
     * Processes repetitive section fields by parsing comma-separated values into array structures.
     * Handles parent~child column relationships, creates appropriate JSON array nodes,
     * and applies field-specific transformations based on input types like picklists.
     */
    public void handleRepetitiveSection(String columnName, Map<String, Object> rowContent, ObjectMapper objectMapper, String cellValue, JsonNode fieldNode) {
        List<String> columnParts = Arrays.asList(columnName.split("~"));
        String parentField = columnParts.get(0);
        String nestedField = columnParts.get(1);
        ArrayNode tabularArrayNode = (ArrayNode) rowContent.getOrDefault(parentField, objectMapper.createArrayNode());
        String[] values = cellValue.split(",");
        while (tabularArrayNode.size() < values.length) {
            tabularArrayNode.add(objectMapper.createObjectNode());
        }
        JsonNode defaultValues = fieldNode.get("displayProperty").get("defaultValues");
        for (int z = 0; z < values.length; z++) {
            ObjectNode contentNode = (ObjectNode) tabularArrayNode.get(z);
            for (JsonNode x : defaultValues) {
                if (!x.fields().hasNext()) continue;
                JsonNode innerNode = x.elements().next();
                String inputType = innerNode.has("inputType") ? innerNode.get("inputType").asText() : "Unknown";

                switch (inputType) {
                    case "Alphanumeric":
                        try {
                            contentNode.put(columnName, Long.parseLong(values[z]));
                        } catch (NumberFormatException e) {
                            contentNode.put(columnName, 0L);
                        }
                        break;

                    case "Searchable picklist":
                    case "Advance Picklist":
                        ObjectNode specialPicklistNode = objectMapper.createObjectNode();
                        List<EntityJPA> jpa = entityRepository.findByName(values[z]);
                        if (!jpa.isEmpty()) {
                            specialPicklistNode.put("id", jpa.get(0).getId());
                        } else {
                            specialPicklistNode.put("id", 1234);
                        }
                        specialPicklistNode.put("name", values[z]);
                        specialPicklistNode.putArray("details");
                        contentNode.set(nestedField, specialPicklistNode);
                        break;

                    default:
                        contentNode.put(nestedField, values[z]);
                        break;
                }
            }

        }
        rowContent.put(parentField, tabularArrayNode);
    }

    /**
     * Processes searchable picklist fields by creating structured objects with ID and name.
     * Looks up entity IDs by name in the database and creates JSON objects
     * with id, name, and details properties for picklist field values.
     */
    public void handleSearchablePicklist(ObjectMapper objectMapper, String cellValue, Map<String, Object> rowContent, String columnName) {
        ObjectNode specialPicklistNode = objectMapper.createObjectNode();
        List<EntityJPA> jpa = entityRepository.findByName(cellValue);
        if (jpa.isEmpty()) {
            specialPicklistNode.put("id", "");
        } else {
            specialPicklistNode.put("id", jpa.get(0).getId());
        }

        specialPicklistNode.put("name", cellValue);
        specialPicklistNode.putArray("details");
        rowContent.put(columnName, specialPicklistNode);
    }

    /**
     * Retrieves configuration values from the batch configuration table as a string array.
     * Queries the database for specific configuration keys and parses JSON arrays
     * into ArrayList of strings for batch processing configuration.
     */
    public ArrayList<String> getConfigValue(String key, String configName) {
        try {
            String result = jdbcTemplate.queryForObject(FETCH_QUERY_KEY_FROM_CONFIG_QUERY, new Object[]{key, configName}, String.class);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result);
                List<String> list = new ArrayList<>();

                if (jsonNode.isArray()) {
                    for (JsonNode node : jsonNode) {
                        list.add(node.asText());
                    }
                }
                return new ArrayList<>(list);
            }
            return null;
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (IOException e) {
            throw new RuntimeException("Error parsing JSON result", e);
        }
    }

    /**
     * Retrieves filter configuration values from the batch configuration table.
     * Specifically queries for 'filterChild' configuration arrays and converts
     * them to ArrayList of strings for filtering operations during batch processing.
     */
    public ArrayList<String> getfilterValue(String key, String configName) {
        try {
            String result = jdbcTemplate.queryForObject(FETCH_QUERY_FILTER_KEY_FROM_CONFIG_QUERY, new Object[]{key, configName}, String.class);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result);
                List<String> list = new ArrayList<>();

                if (jsonNode.isArray()) {
                    for (JsonNode node : jsonNode) {
                        list.add(node.asText());
                    }
                }
                return new ArrayList<>(list);
            }
            return null;
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (IOException e) {
            throw new RuntimeException("Error parsing JSON result", e);
        }
    }

    /**
     * Retrieves partition configuration values as JsonNode for partitioning strategies.
     * Queries the database for partition-specific configuration and returns
     * the raw JsonNode for flexible partition configuration handling.
     */
    public JsonNode getPartitionValues(String key, String configName) {
        try {
            String result = jdbcTemplate.queryForObject(FETCH_QUERY_PARTITION_KEY_FROM_CONFIG_QUERY, new Object[]{key, configName}, String.class);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result);
                return jsonNode;
            }
            return null;
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (IOException e) {
            throw new RuntimeException("Error parsing JSON result", e);
        }
    }

    /**
     * Retrieves general configuration values as JsonNode for flexible configuration access.
     * Queries the database for any configuration key and returns the raw JsonNode
     * allowing for complex configuration structures and dynamic processing.
     */
    public JsonNode getConfigValues(String key, String configName) {
        try {
            String result = jdbcTemplate.queryForObject(FETCH_CONFIG_FROM_CONFIG_QUERY, new Object[]{key, configName}, String.class);
            if (result != null) {
                JsonNode jsonNode = objectMapper.readTree(result);
                return jsonNode;
            }
            return null;
        } catch (EmptyResultDataAccessException e) {
            return null;
        } catch (IOException e) {
            throw new RuntimeException("Error parsing JSON result", e);
        }
    }

    /**
     * Retrieves the highest numeric value for a specific key from deal assets.
     * Queries deal asset objects to find the maximum value for auto-incrementing
     * fields or sequence generation in batch processing operations.
     */
    public Long getLastIdBasedOnKey(String key, Long bnusinessProcessId) {
        try {
            String result = jdbcTemplate.queryForObject(fetchlastNumnber, new Object[]{key, bnusinessProcessId, key}, String.class);
            if (result != null) {
                Long value = Long.valueOf((result));
                return value;
            }
            return null;
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

}
