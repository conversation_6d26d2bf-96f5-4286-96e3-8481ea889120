package org.apache.fineract.cn.originate.service.SpringBatch.Command;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.DealBatchDomain;
/**
 * Command class for deal CSV batch processing operations.
 * Encapsulates the deal batch domain data required for executing
 * batch import and processing operations for deal entities.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DealCsvBatchCommand {
    private DealBatchDomain dealBatchDomain;
}
