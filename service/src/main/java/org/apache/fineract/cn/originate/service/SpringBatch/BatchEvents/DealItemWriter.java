package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealRepository;
import org.slf4j.Logger;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

public class DealItemWriter implements ItemWriter<DealEntity> {
    private final String sheetName;
    private final String url;
    @Autowired
    private DealRepository dealRepository;
    @Autowired
    @Qualifier("originate-logger")
    private Logger logger;


    /**
     * Constructor that initializes the DealItemWriter with sheet name and URL parameters.
     * Sets up the writer configuration for processing deal entities from the specified
     * data source location.
     */
    @Autowired
    public DealItemWriter(
            String sheetName,
            String url) throws Exception {
        this.sheetName = sheetName;
        this.url = url;
    }


    /**
     * Callback method executed before the step starts.
     *
     * @param stepExecution The StepExecution instance
     */
    @BeforeStep
    public void saveFilePath(StepExecution stepExecution) {
        // Implementation for pre-step processing if needed
    }

    /**
     * Writes the provided Deal entities to the repository.
     *
     * @param items List of DealEntity objects to be written
     * @throws Exception if writing fails
     */
    @Override
    public void write(List<? extends DealEntity> items) throws Exception {
        logger.info("Inside writer");
        if (items.isEmpty()) {
            return;
        }
        try {
            logger.info("trying to save deal entity of size {}", items.size());
            dealRepository.save(items);
        } catch (Exception e) {
            logger.error("Failed to save deal entities: {}", e.getMessage(), e);
            throw e;
        }
    }
}
