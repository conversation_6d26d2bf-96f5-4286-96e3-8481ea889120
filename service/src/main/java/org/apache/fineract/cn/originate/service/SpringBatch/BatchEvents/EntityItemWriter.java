package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityRepository;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionRepository;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.*;

public class EntityItemWriter implements ItemWriter<EntityJPA> {
    @Autowired
    private EntityRepository entityRepository;
    @Autowired
    private EntityDefinitionRepository entityDefinitionRepository;

    /**
     * Constructor that initializes the EntityItemWriter with sheet name and URL parameters.
     * Currently empty implementation but provides structure for future initialization
     * of writer configuration based on sheet and URL parameters.
     */
    @Autowired
    public EntityItemWriter(String sheetName, String url) throws Exception {

    }

    /**
     * Pre-step initialization method that executes before the step begins.
     * Currently empty implementation but provides hook for saving file path
     * or other step-specific initialization based on step execution context.
     */
    @BeforeStep
    public void saveFilePath(StepExecution stepExecution) {

    }


    /**
     * Main writing method that persists a list of EntityJPA objects to the database.
     * Checks if the items list is empty before attempting to save, ensuring
     * efficient batch processing by avoiding unnecessary database operations.
     */
    @Override
    public void write(List<? extends EntityJPA> items) throws Exception {
        if (items.isEmpty()) {
            return;
        }
        entityRepository.save(items);
    }

}

