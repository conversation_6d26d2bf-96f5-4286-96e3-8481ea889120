package org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils;

import org.apache.fineract.cn.originate.service.SpringBatch.BatchDomain.BatchStatusDomain;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class BatchStatusRowMapper implements RowMapper<BatchStatusDomain> {
    /**
     * Maps a database result set row to a BatchStatusDomain object.
     * Extracts batch execution metadata including step execution details, timing information,
     * counts for read/write/filter operations, and status information from the database row.
     */
    @Override
    public BatchStatusDomain mapRow(ResultSet rs, int rowNum) throws SQLException {
        BatchStatusDomain domain = new BatchStatusDomain();
        domain.setStepExecutionId(rs.getLong("step_execution_id"));
        domain.setValidationErrorCount(rs.getInt("validation_error_count"));
        domain.setStepName(rs.getString("step_name"));
        domain.setJobExecutionId(rs.getLong("job_execution_id"));
        domain.setStartTime(rs.getTimestamp("start_time") != null ? rs.getTimestamp("start_time").getTime() : 0L);
        domain.setEndTime(rs.getTimestamp("end_time") != null ? rs.getTimestamp("end_time").getTime() : 0L);
        domain.setStatus(rs.getString("status"));
        domain.setReadCount(rs.getInt("read_count"));
        domain.setFilterCount(rs.getInt("filter_count"));
        domain.setWriteCount(rs.getInt("write_count"));
        domain.setExitCode(rs.getString("exit_code"));
        domain.setExitMessage(rs.getString("exit_message"));
        domain.setLastUpdated(rs.getTimestamp("last_updated") != null ? rs.getTimestamp("last_updated").getTime() : 0L);
        return domain;
    }

}

