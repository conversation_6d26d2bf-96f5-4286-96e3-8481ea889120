package org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.fineract.cn.originate.api.v1.domain.deal.*;
import org.apache.fineract.cn.originate.api.v1.domain.reqdoc.DocumentDomain;
import org.springframework.jdbc.core.RowMapper;
import org.postgresql.util.PGobject;

import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

public class DealRowMapper<D> implements RowMapper<Deal> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Maps a database result set row to a Deal domain object.
     * Extracts deal information from database columns including JSON fields,
     * handles complex object mappings, and provides error handling for data conversion.
     */
    @Override
    public Deal mapRow(ResultSet rs, int rowNum) throws SQLException {
        Deal deal = new Deal();

        try {
            deal.setId(rs.getLong("deal_id"));
            deal.setCurrentStageName(rs.getString("current_stage_name"));
            deal.setCurrentStatus(rs.getString("current_status"));
            deal.setPreviousStageName(rs.getString("previous_stage_name"));
            deal.setPurpose(rs.getString("purpose"));
            deal.setDealAmount(rs.getBigDecimal("deal_amount"));
            deal.setSector(rs.getString("sector"));
            deal.setStartDate(rs.getString("start_date"));
            deal.setCreatedBy(rs.getString("created_by"));
            deal.setDealIdentifier(rs.getString("deal_identifier"));
            deal.setCurrency(rs.getString("currency"));
            deal.setRejectedReason(rs.getString("rejected_reason"));
            deal.setCreatedDate(rs.getString("created_date"));
            deal.setModifiedBy(rs.getString("modified_by"));
            deal.setModifiedDate(rs.getString("modified_date"));

            // JSON object mappings
            deal.setDealAssetObj(readJson(rs, "deal_asset_obj", JsonNode.class));

            // Handle deal_asset as JsonNode first
            JsonNode dealAssetNode = readJson(rs, "deal_asset", JsonNode.class);
            if (dealAssetNode != null) {
                if (dealAssetNode.isArray()) {
                    // If it's an array, take the first element
                    if (dealAssetNode.size() > 0) {
                        DealAsset dealAsset = objectMapper.treeToValue(dealAssetNode.get(0), DealAsset.class);
                        deal.setDealAsset(dealAsset);
                    }
                } else {
                    // If it's not an array, convert directly
                    DealAsset dealAsset = objectMapper.treeToValue(dealAssetNode, DealAsset.class);
                    deal.setDealAsset(dealAsset);
                }
            }

        } catch (Exception e) {
            throw new SQLException("Error mapping Deal object", e);
        }

        return deal;
    }

    /**
     * Reads and deserializes JSON data from a PostgreSQL JSON column to a specified type.
     * Configures ObjectMapper for lenient parsing and handles PostgreSQL-specific
     * JSON object extraction with comprehensive error handling.
     */
    private <T> T readJson(ResultSet rs, String column, Class<T> valueType) throws SQLException {
        PGobject pgObject = (PGobject) rs.getObject(column);
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                String jsonValue = pgObject.getValue();
                // Configure ObjectMapper for more lenient parsing
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

                return objectMapper.readValue(jsonValue, valueType);
            } catch (IOException e) {
                throw new SQLException(
                        String.format("Failed to deserialize column %s into %s. JSON value: %s. Error: %s",
                                column,
                                valueType.getSimpleName(),
                                pgObject.getValue(),
                                e.getMessage()
                        ),
                        e
                );
            }
        }
        return null;
    }

    /**
     * Reads and deserializes JSON array data from a PostgreSQL column to a List of specified type.
     * Handles JSON array parsing and converts to strongly-typed collections,
     * returning empty list if no data is found to prevent null pointer exceptions.
     */
    private <T> List<T> readJsonList(ResultSet rs, String column, Class<T> elementType) throws SQLException {
        PGobject pgObject = (PGobject) rs.getObject(column);
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                return objectMapper.readValue(pgObject.getValue(), objectMapper.getTypeFactory().constructCollectionType(List.class, elementType));
            } catch (IOException e) {
                throw new SQLException("Failed to deserialize column " + column + " into List<" + elementType.getSimpleName() + ">", e);
            }
        }
        return Collections.emptyList();
    }

    /**
     * Reads JSON data from a PostgreSQL column and returns it as a JsonNode.
     * Provides raw JSON access for flexible processing without type conversion,
     * useful for dynamic JSON manipulation and inspection.
     */
    private JsonNode readJsonNode(ResultSet rs, String column) throws SQLException {
        PGobject pgObject = (PGobject) rs.getObject(column);
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                return objectMapper.readTree(pgObject.getValue());
            } catch (IOException e) {
                throw new SQLException("Failed to deserialize column " + column + " into JsonNode", e);
            }
        }
        return null;
    }

}