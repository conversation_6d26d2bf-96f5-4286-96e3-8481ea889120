package org.apache.fineract.cn.originate.service.SpringBatch.Command;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.DealBatchDomain;

/**
 * Command class for complex deal CSV batch update operations.
 * Encapsulates the deal batch domain data required for executing
 * complex batch update operations with advanced field processing and merging.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateComplexDealCsvBatchCommand {
    private DealBatchDomain dealBatchDomain;
}
