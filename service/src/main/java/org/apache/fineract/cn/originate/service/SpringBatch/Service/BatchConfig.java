package org.apache.fineract.cn.originate.service.SpringBatch.Service;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents.*;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.DealComposite;

import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.OriginateBatchConfigurationEntity;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.OriginateBatchConfigurationRepository;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.partition.PartitionHandler;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.core.partition.support.TaskExecutorPartitionHandler;

import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

import com.opencsv.CSVWriter;

import java.io.FileWriter;
import java.io.File;
import java.util.regex.Pattern;

@Configuration
@EnableBatchProcessing
public class BatchConfig {
    private static final Logger logger = LoggerFactory.getLogger(BatchConfig.class);
    private static final String ENTITY_DEFINITION = "entityDefinition";
    private final JobBuilderFactory jobBuilderFactory;
    private final StepBuilderFactory stepBuilderFactory;
    private final ObjectMapper mapper;
    private final JobLauncher jobLauncher;
    private EntityDefinitionEntity entityDefinitionEntity;
    private String newFilePath;


    @Autowired
    private BusinessProcessDefinitionRepository businessProcessDefinitionRepository;
    @Autowired
    private EntityDefinitionRepository entityDefinitionRepository;
    @Autowired
    private OriginateBatchConfigurationRepository originateBatchConfigurationRepository;
    @Autowired
    private BatchUtils utils;

    private BusinessProcessDefinitionEntity cachedBusinessProcessDefinition;

    @Autowired
    public BatchConfig(JobBuilderFactory jobBuilderFactory, StepBuilderFactory stepBuilderFactory, ObjectMapper mapper, JobLauncher jobLauncher) {
        this.jobBuilderFactory = jobBuilderFactory;
        this.stepBuilderFactory = stepBuilderFactory;
        this.mapper = mapper;
        this.jobLauncher = jobLauncher;
    }

    public static String replaceSpecialCharacters(String input) {
        Pattern pattern = Pattern.compile("\\s*([&+\\-*/()\\[\\]{}])\\s*|\\s+");
        String intermediate = pattern.matcher(input).replaceAll("_");
        String result = intermediate.replaceAll("_+", "_");
        result = result.replaceAll("^_+", "").replaceAll("_+$", "");

        return result;
    }

    public static String toCamelCase(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.substring(0, 1).toLowerCase() + input.substring(1);
    }

    @Bean(name = "entityCsvBatchInsertJob")
    public Job entity_Csv_Batch_Insert_Job(EntityJobCompletionNotificationListener notificationListener) throws Exception {
        return jobBuilderFactory.get("entity_Csv_Batch_Insert_Job")
                .start(fetchEntityDefinition())
                .next(entityValidationStep(null))
                .next(entity_csv_process_step(null, null, null, null))
                .listener(notificationListener)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    @Bean(name = "dealCsvBatchInsertJob")
    public Job deal_Batch_Insert_Job(DealCsvJobCompletionNotificationListner notificationListener) throws Exception {
        return jobBuilderFactory.get("deal_Batch_Insert_Job")
                .start(fetchBusinessProcessDefinition())
                .next(autoIDGeneration(null))
                .next(dealValidationStep(null))
                .next(deal_process_step(null, null, 0L))
                .listener(notificationListener)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    @Bean(name = "deal_Simple_Batch_Update_Job")
    public Job deal_Simple_Batch_Update_Job(DealCsvJobCompletionNotificationListner notificationListener) throws Exception {
        return jobBuilderFactory.get("deal_Simple_Batch_Update_Job")
                .start(fetchBusinessProcessDefinition())
                .next(dealValidationStep(null))
                .next(update_deal_process_step(null, null, 0L))
                .listener(notificationListener)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    @Bean(name = "dealCsvBatchUpdateJob")
    public Job deal_Batch_Update_Job(DealCsvJobCompletionNotificationListner notificationListener) throws Exception {
        return jobBuilderFactory.get("deal_Batch_update_Job")
                .start(fetchBusinessProcessDefinition())
                .next(dealValidationStep(null))
                .next(partitionUpdateDealStep(0))
                .listener(notificationListener)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    @Bean(name = "entityCsvBatchUpdateJob")
    public Job entity_Csv_Batch_Update_Job(EntityJobCompletionNotificationListener notificationListener) throws Exception {
        return jobBuilderFactory.get("entity_Csv_Batch_Update_Job")
                .start(fetchEntityDefinition())
                .next(entityValidationStep(null))
                .next(updateEntityStep(null, null, null, null))
                .listener(notificationListener)
                .incrementer(new RunIdIncrementer())
                .build();
    }

    @Bean
    @JobScope
    public Step partitionUpdateDealStep(@Value("#{jobParameters['gridSize']}") Integer gridSize) throws Exception {
        return stepBuilderFactory.get("partitionUpdateDealStep")
                .partitioner("updateDealPartitionStep", dealPartitioner())
                .step(updateDealPartitionStep())
                .gridSize(Math.toIntExact(gridSize))
                .taskExecutor(batchPartitionTaskExecutor())
                .partitionHandler(partitionHandler(updateDealPartitionStep(), gridSize))
                .build();
    }

    public PartitionHandler partitionHandler(Step updateDealPartitionStep, @Value("#{jobParameters['gridSize']}") Integer gridSize) throws Exception {
        TaskExecutorPartitionHandler handler = new TaskExecutorPartitionHandler();
        handler.setTaskExecutor(batchPartitionTaskExecutor());
        handler.setStep(updateDealPartitionStep());
        handler.setGridSize(Math.toIntExact(gridSize));
        return handler;
    }

    @Bean
    public Partitioner dealPartitioner() {
        return new DealPartitioner();
    }

    @Bean
    public Step updateDealPartitionStep() throws Exception {
        return stepBuilderFactory.get("updateDealPartitionStep")
                .<JsonNode, DealComposite>chunk(200)
                .reader(partitionedUpdateDealReader(null))
                .processor(updateComplexDealItemProcessor(null))
                .writer(updateComplexDealCsvItemWriter(null, null))
                .listener(partitionStepExecutionListener())
                .build();
    }

    @Bean
    @JobScope
    public Step entity_csv_process_step(@Value("#{jobParameters[URL]}") String url, @Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[entityType]}") String entityType, @Value("#{jobParameters[entityName]}") String entityName) throws Exception {
        return stepBuilderFactory.get("entity_csv_process_step").<JsonNode, EntityJPA>chunk(1)
                .reader(entityCsvItemReader(url, sheetName))
                .processor(entityCsvItemProcessor(entityType, entityName))
                .writer(entityCsvItemWriter(sheetName, url))
//                .taskExecutor(new SimpleAsyncTaskExecutor())
//                .throttleLimit(Runtime.getRuntime().availableProcessors())
                .build();
    }

//    @Bean
//    @JobScope
//    public Step update_deal_process_step(@Value("#{jobParameters[URL]}") String url, @Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[businessProcessId]}") Long businessProcessId) throws Exception {
//        return stepBuilderFactory.get("update_deal_process_step")
//                .<JsonNode, DealComposite>chunk( 3)
//                .reader(updateDealCsvItemReader(url, sheetName))
//                .processor(updateDealItemProcessor(businessProcessId))
//                .writer(updateDealCsvItemWriter(sheetName, url))
//                .taskExecutor(updateDealTaskExecutor())
//                .throttleLimit(fetchConfigBasedOnConfigType("throttleLimit"))
//                .build();
//    }

    @Bean
    @JobScope
    public Step deal_process_step(@Value("#{jobExecutionContext['url'] ?: jobParameters[URL]}") String url, @Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[businessProcessId]}") Long businessProcessId) throws Exception {
        return stepBuilderFactory.get("deal_process_step").<JsonNode, DealEntity>chunk(fetchConfigBasedOnConfigType("chunkSize"))
                .reader(dealCsvItemReader(url, sheetName))
                .processor(dealItemProcessor(businessProcessId))
                .writer(dealCsvItemWriter(sheetName, url))
//                .taskExecutor(new SimpleAsyncTaskExecutor())
//                .throttleLimit(Runtime.getRuntime().availableProcessors())
                .build();
    }

    @Bean
    @JobScope
    public Step update_deal_process_step(@Value("#{jobParameters[URL]}") String url, @Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[businessProcessId]}") Long businessProcessId) throws Exception {
        return stepBuilderFactory.get("update_complex_deal_process_step")
                .<JsonNode, DealComposite>chunk(3)
                .reader(updateDealCsvItemReader(url, sheetName))
                .processor(updateDealItemProcessor(businessProcessId))
                .writer(updateDealCsvItemWriter(sheetName, url))
                .taskExecutor(new SimpleAsyncTaskExecutor())
                .throttleLimit(Runtime.getRuntime().availableProcessors())
                .build();
    }

    @Bean
    @JobScope
    public Step updateEntityStep(@Value("#{jobParameters[URL]}") String url, @Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[entityType]}") String entityType, @Value("#{jobParameters[entityName]}") String entityName) throws Exception {
        return stepBuilderFactory.get("entity_csv_update_step").<JsonNode, EntityJPA>chunk(2000)
                .reader(updateEntityItemReader(url))
                .processor(updateEntityItemProcessor(entityType, entityName))
                .writer(updateEntityItemWriter(sheetName, url))
                .taskExecutor(updateDealTaskExecutor())
                .throttleLimit(3)
                .build();
    }

    @Bean
    public Step fetchEntityDefinition() {
        return stepBuilderFactory.get("fetchEntityDefinitionStep").tasklet((contribution, chunkContext) -> {
            JobParameters jobParameters = chunkContext.getStepContext().getStepExecution().getJobParameters();
            String entityType = jobParameters.getString("entityType", null);
            String entityName = jobParameters.getString("entityName", null);
            String entitySubType = jobParameters.getString("entitySubType", null);
            List<EntityDefinitionEntity> listEntityDefinitionEntity = entityDefinitionRepository.
                    findByEntityNameAndSubTypeAndEntityType(entityName, entitySubType, entityType);
            if (listEntityDefinitionEntity.isEmpty()) {
                throw new IllegalArgumentException("Entity definition not found for entityType: " + entityType + " and entityName: " + entityName);
            } else {
                EntityDefinitionEntity entityDefinitionEntity = listEntityDefinitionEntity.get(0);
                putInJobExecutionContext("entityDefinition", entityDefinitionEntity);
            }

            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean
    public Step fetchBusinessProcessDefinition() {
        return stepBuilderFactory.get("fetchBusinessProcessDefinitionStep").tasklet((contribution, chunkContext) -> {
            JobParameters jobParameters = chunkContext.getStepContext().getStepExecution().getJobParameters();
            Long businessProcessId = jobParameters.getLong("businessProcessId", 0);
            Optional<BusinessProcessDefinitionEntity> businessProcessDefinitionEntity = businessProcessDefinitionRepository.findById(businessProcessId);
            if (!businessProcessDefinitionEntity.isPresent()) {
                throw new IllegalArgumentException("Business Process definition not found for Id: " + businessProcessId);
            } else {
                BusinessProcessDefinitionEntity DefinitionEntity = businessProcessDefinitionEntity.get();
                putInJobExecutionContext("businessProcessDefinition", DefinitionEntity);
            }

            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean
    @JobScope
    public Step dealValidationStep(@Value("#{jobExecutionContext['url'] ?: jobParameters[URL]}") String url) {
        return stepBuilderFactory.get("validationStep").tasklet((contribution, chunkContext) -> {
            try {
                System.out.println("Validation Step");
                BusinessProcessDefinitionEntity businessProcessDefinitionEntity = getBusinessProcessDefinition();
                if (businessProcessDefinitionEntity == null) {
                    return null;
                }
                JsonNode fieldDefinitions = businessProcessDefinitionEntity.getAssetItems();

                // Read all rows from the original CSV
                List<String[]> allRows;
                try (CSVReader csvReader = new CSVReader(new InputStreamReader(utils.streamFileFromLocal(url)))) {
                    allRows = csvReader.readAll();
                }

                if (allRows.isEmpty()) {
                    throw new IllegalArgumentException("CSV file is empty or has no headers");
                }

                String[] headers = allRows.get(0);

                // Create error CSV file
                String errorFilePath = url.substring(0, url.lastIndexOf('.')) + "_errors.csv";
                CSVWriter errorWriter = new CSVWriter(new FileWriter(errorFilePath));

                // Write headers plus error reason column to error CSV
                String[] errorHeaders = Arrays.copyOf(headers, headers.length + 1);
                errorHeaders[headers.length] = "Error Reason";
                errorWriter.writeNext(errorHeaders);

                // Map field names to mandatory status
                Map<String, Boolean> mandatoryFields = new HashMap<>();
                for (JsonNode fieldNode : fieldDefinitions) {
                    Iterator<String> fieldNames = fieldNode.fieldNames();
                    while (fieldNames.hasNext()) {
                        String fieldName = fieldNames.next();
                        JsonNode field = fieldNode.get(fieldName);

                        // Check if field is mandatory in any stage
                        boolean isMandatory = false;
                        if (field.has("stages")) {
                            for (JsonNode stage : field.get("stages")) {
                                if (stage.has("isMandatory") &&
                                        !stage.get("isMandatory").asText().isEmpty() &&
                                        stage.get("isMandatory").asText().equalsIgnoreCase("Y")) {
                                    isMandatory = true;
                                    break;
                                }
                            }
                        }
                        mandatoryFields.put(fieldName, isMandatory);
                    }
                }

                // Create list for valid rows (starting from row 1 to skip headers)
                List<String[]> validRows = new ArrayList<>();
                validRows.add(headers); // Add headers first

                boolean hasErrors = false;

                // Validate each data row (skip header row)
                int errorCount = 0;
                for (int rowNum = 1; rowNum < allRows.size(); rowNum++) {
                    String[] row = allRows.get(rowNum);
                    boolean rowHasError = false;
                    String errorReason = "";

                    for (int i = 0; i < headers.length; i++) {
                        String columnName = headers[i];
                        String value = i < row.length ? row[i] : "";

                        if (mandatoryFields.containsKey(toCamelCase(columnName)) &&
                                Boolean.TRUE.equals(mandatoryFields.get(toCamelCase(replaceSpecialCharacters(columnName))))) {
                            if (value == null || value.trim().isEmpty()) {
                                rowHasError = true;
                                errorReason += "Mandatory field '" + columnName + "' is null; ";
                            }
                        }
                    }

                    if (rowHasError) {
                        hasErrors = true;
                        errorCount++;
                        String[] errorRow = Arrays.copyOf(row, row.length + 1);
                        errorRow[row.length] = errorReason.trim();
                        errorWriter.writeNext(errorRow);
                    } else {
                        validRows.add(row); // Add to valid rows
                    }
                }
                chunkContext.getStepContext().getStepExecution().getExecutionContext().put("validationErrorCount", errorCount);

                errorWriter.close();

                // Store error file path in job context if errors were found
                if (hasErrors) {
                    putInJobExecutionContext("errorFilePath", errorFilePath);
                    putInJobExecutionContext("hasValidationErrors", true);

                    // Write the valid rows back to the original file
                    try (CSVWriter validWriter = new CSVWriter(new FileWriter(url))) {
                        validWriter.writeAll(validRows);
                    }
                } else {
                    // Delete error file if no errors
                    if (new File(errorFilePath).delete()) {
                        logger.info("Error file deleted");
                    } else {
                        logger.info("Error file saved to {} directory", (errorFilePath));
                    }
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
            return RepeatStatus.FINISHED;
        }).build();
    }


    @Bean
    @JobScope
    public Step entityValidationStep(@Value("#{jobParameters[URL]}") String url) {
        return stepBuilderFactory.get("entityValidationStep").tasklet((contribution, chunkContext) -> {
            logger.info("Entity validation step");
            EntityDefinitionEntity entityDefinitionEntity = getEntityDefinition();
            if (entityDefinitionEntity == null) {
                return null;
            }
            JsonNode fieldDefinitions = entityDefinitionEntity.getEntityDetail().get("entityDetail");

            // Read all rows from the original CSV
            List<String[]> allRows;
            try (CSVReader csvReader = new CSVReader(new InputStreamReader(utils.streamFileFromLocal(url)))) {
                allRows = csvReader.readAll();
            }

            if (allRows.isEmpty()) {
                throw new IllegalArgumentException("CSV file is empty");
            }

            String[] headers = allRows.get(0);

            // Create error CSV file
            String errorFilePath = url.substring(0, url.lastIndexOf('.')) + "_errors.csv";
            CSVWriter errorWriter = new CSVWriter(new FileWriter(errorFilePath));

            // Write headers plus error reason column to error CSV
            String[] errorHeaders = Arrays.copyOf(headers, headers.length + 1);
            errorHeaders[headers.length] = "Error Reason";
            errorWriter.writeNext(errorHeaders);

            // Map field names to mandatory status
            Map<String, Boolean> mandatoryFields = new HashMap<>();
            for (JsonNode fieldNode : fieldDefinitions) {
                Iterator<String> fieldNames = fieldNode.fieldNames();
                while (fieldNames.hasNext()) {
                    String fieldName = fieldNames.next();
                    JsonNode field = fieldNode.get(fieldName);

                    // Check if field is mandatory
                    boolean isMandatory = false;
                    if (field.has("stages")) {
                        for (JsonNode stage : field.get("stages")) {
                            if (stage.has("isMandatory") &&
                                    !stage.get("isMandatory").asText().isEmpty() &&
                                    stage.get("isMandatory").asText().equalsIgnoreCase("Y")) {
                                isMandatory = true;
                                break;
                            }
                        }
                    }
                    mandatoryFields.put(fieldName, isMandatory);
                }
            }

            // Create list for valid rows (starting from row 1 to skip headers)
            List<String[]> validRows = new ArrayList<>();
            validRows.add(headers); // Add headers first

            boolean hasErrors = false;

            // Process each data row (skip header row)
            for (int rowNum = 1; rowNum < allRows.size(); rowNum++) {
                String[] row = allRows.get(rowNum);
                boolean rowHasError = false;
                String errorReason = "";

                for (int i = 0; i < headers.length; i++) {
                    String columnName = headers[i];
                    String value = i < row.length ? row[i] : "";

                    if (mandatoryFields.containsKey(toCamelCase(columnName)) &&
                            Boolean.TRUE.equals(mandatoryFields.get(toCamelCase(replaceSpecialCharacters(columnName))))) {
                        if (value == null || value.trim().isEmpty()) {
                            rowHasError = true;
                            errorReason += "Mandatory field '" + columnName + "' is null; ";
                        }
                    }
                }
                int errorCount = 0;
                if (rowHasError) {
                    hasErrors = true;
                    errorCount++;
                    String[] errorRow = Arrays.copyOf(row, row.length + 1);
                    errorRow[row.length] = errorReason.trim();
                    errorWriter.writeNext(errorRow);
                } else {
                    validRows.add(row); // Add to valid rows
                }
                chunkContext.getStepContext().getStepExecution().getExecutionContext().put("validationErrorCount", errorCount);
            }

            errorWriter.close();

            // Store error file path in job context if errors were found
            if (hasErrors) {
                putInJobExecutionContext("errorFilePath", errorFilePath);
                putInJobExecutionContext("hasValidationErrors", true);

                // Write the valid rows back to the original file
                try (CSVWriter validWriter = new CSVWriter(new FileWriter(url))) {
                    validWriter.writeAll(validRows);
                }
            } else {
                // Delete error file if no errors
                if (new File(errorFilePath).delete()) {
                    logger.info("Error file deleted");
                } else {
                    logger.info("Error file not deleted");
                }
            }

            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean
    @JobScope
    public Step autoIDGeneration(@Value("#{jobParameters[URL]}") String url) {
        return stepBuilderFactory.get("autoIDGenerationStep").tasklet((contribution, chunkContext) -> {
            boolean isAutoIdEnabled = false;
            Map<String, Long> fieldCutoffMap = new HashMap<>();

            Optional<OriginateBatchConfigurationEntity> batchConfigEntity =
                    originateBatchConfigurationRepository.findByConfigurationName("Batch_Auto_ID");

            if (batchConfigEntity.isPresent()) {
                logger.info("Batch Auto ID Configuration found");
                try {
                    JsonNode configDetails = mapper.readTree(batchConfigEntity.get().getConfigurationDetails().traverse());
                    if (configDetails.has("isAutoIdEnabled")) {
                        isAutoIdEnabled = configDetails.get("isAutoIdEnabled").asBoolean();
                    }
                    if (isAutoIdEnabled && configDetails.has("fieldName")) {
                        JsonNode fieldNameNode = configDetails.get("fieldName");
                        Iterator<Map.Entry<String, JsonNode>> fields = fieldNameNode.fields();
                        while (fields.hasNext()) {
                            Map.Entry<String, JsonNode> field = fields.next();
                            String fieldName = field.getKey();
                            long cutOffValue = field.getValue().asLong();
                            fieldCutoffMap.put(fieldName, cutOffValue);
                            logger.info("Found field configuration: {} with cutoff value: {}", fieldName, cutOffValue);
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error parsing configuration: {}", e.getMessage());
                }
            }

            if (!isAutoIdEnabled) {
                logger.info("Auto ID Generation is disabled. Skipping.");
                return RepeatStatus.FINISHED;
            }
            if (fieldCutoffMap.isEmpty()) {
                logger.info("No fields configured for auto ID generation. Skipping.");
                return RepeatStatus.FINISHED;
            }

            List<String[]> rows;
            try (CSVReader reader = new CSVReader(new InputStreamReader(utils.streamFileFromLocal(url)))) {
                rows = reader.readAll();
            }
            if (rows.isEmpty()) {
                throw new IllegalArgumentException("CSV is empty. Exiting.");
            }

            String[] headers = rows.get(0);
            Map<Integer, String> columnIndexToFieldMap = new HashMap<>();
            Map<String, Long> fieldToCurrentIdMap = new HashMap<>();

            // Initialize starting IDs from DB or cutoff + 1
            for (String configuredField : fieldCutoffMap.keySet()) {
                Long storedKey = utils.getLastIdBasedOnKey(configuredField,
                        Objects.requireNonNull(getBusinessProcessDefinition()).getId());
                long startingId = (storedKey == null)
                        ? fieldCutoffMap.get(configuredField) + 1
                        : storedKey + 1;
                fieldToCurrentIdMap.put(configuredField, startingId);
                logger.info("Initialized {} with starting ID {}", configuredField, startingId);
            }

            // Map CSV columns to configured fields
            for (int i = 0; i < headers.length; i++) {
                String header = headers[i];
                for (String configuredField : fieldCutoffMap.keySet()) {
                    if (header.equalsIgnoreCase(configuredField)) {
                        columnIndexToFieldMap.put(i, configuredField);
                        logger.info("Matched CSV column '{}' to configured field '{}'", header, configuredField);
                        break;
                    }
                }
            }

            if (columnIndexToFieldMap.isEmpty()) {
                logger.warn("No configured fields found in CSV headers. Skipping ID generation.");
                return RepeatStatus.FINISHED;
            }

            // Generate IDs
            for (int i = 1; i < rows.size(); i++) {
                String[] row = rows.get(i);
                for (Map.Entry<Integer, String> entry : columnIndexToFieldMap.entrySet()) {
                    int columnIndex = entry.getKey();
                    String fieldName = entry.getValue();

                    if (columnIndex < row.length && (row[columnIndex] == null || row[columnIndex].trim().isEmpty())) {
                        long currentId = fieldToCurrentIdMap.get(fieldName);
                        row[columnIndex] = String.format("%07d", currentId);
                        fieldToCurrentIdMap.put(fieldName, currentId + 1);
                    }
                }
            }

            // Write to temp file
            String fileName = Paths.get(url).getFileName().toString().replace(".csv", "");
            File csvFile = Files.createTempFile("EXP_" + fileName + "_", ".csv").toFile();
            try (CSVWriter writer = new CSVWriter(new FileWriter(csvFile))) {
                writer.writeAll(rows);
            }
            putInJobExecutionContext("url", csvFile.getAbsolutePath());
            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean
    @JobScope
    public ItemWriter itemWriter() {
        return new ItemWriter() {
            @Override
            public void write(List list) throws Exception {
            }
        };
    }

    @Bean
    @StepScope
    public EntityItemReader entityCsvItemReader(@Value("#{jobParameters[URL]}") String excelFilePath, @Value("#{jobParameters[sheetName]}") String sheetName) throws IOException, CsvException {
        return new EntityItemReader(excelFilePath);
    }

    @Bean
    @StepScope
    public EntityItemProcessor entityCsvItemProcessor(@Value("#{jobParameters[entityType]}") String entityType, @Value("#{jobParameters[entityName]}") String entityName) {
        return new EntityItemProcessor(entityType, entityName);
    }

    @Bean
    @StepScope
    public EntityItemWriter entityCsvItemWriter(@Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[URL]}") String url) throws Exception {
        return new EntityItemWriter(sheetName, url);
    }

    @Bean
    @StepScope
    public UpdateEntityItemWriter updateEntityItemWriter(@Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[URL]}") String url) throws Exception {
        return new UpdateEntityItemWriter();
    }

    @Bean
    @JobScope
    public EntityJobCompletionNotificationListener notificationListener() {
        return new EntityJobCompletionNotificationListener();
    }

    @Bean
    @StepScope
    public DealItemReader dealCsvItemReader(@Value("#{jobExecutionContext['url'] ?: jobParameters[URL]}") String excelFilePath, @Value("#{jobParameters[sheetName]}") String sheetName) throws IOException, CsvException {
        return new DealItemReader(excelFilePath);
    }

    @Bean
    @StepScope
    public DealItemProcessor dealItemProcessor(@Value("#{jobParameters[businessProcessId]}") Long businessProcessId) {
        return new DealItemProcessor(businessProcessId);
    }

    @Bean
    @StepScope
    public DealItemWriter dealCsvItemWriter(@Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobExecutionContext['url'] ?: jobParameters[URL]}") String url) throws Exception {
        return new DealItemWriter(sheetName, url);
    }

    @Bean
    @JobScope
    public DealCsvJobCompletionNotificationListner dealNotificationListener() {
        return new DealCsvJobCompletionNotificationListner();
    }

    @Bean
    @StepScope
    public UpdateDealItemReader updateDealCsvItemReader(@Value("#{jobParameters[URL]}") String excelFilePath, @Value("#{jobParameters[sheetName]}") String sheetName) throws IOException, CsvException {
        return new UpdateDealItemReader(excelFilePath);
    }

    @Bean
    @StepScope
    public UpdateDealItemProcessor updateDealItemProcessor(@Value("#{jobParameters[businessProcessId]}") Long businessProcessId) {
        return new UpdateDealItemProcessor(businessProcessId);
    }

    @Bean
    @StepScope
    public UpdateDealItemWriter updateDealCsvItemWriter(@Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[URL]}") String url) throws Exception {
        return new UpdateDealItemWriter(sheetName, url);
    }

    @Bean
    @StepScope
    public UpdateComplexDealItemReader updateComplexDealCsvItemReader(@Value("#{jobParameters[URL]}") String excelFilePath, @Value("#{jobParameters[sheetName]}") String sheetName) throws IOException, CsvException {
        return new UpdateComplexDealItemReader(excelFilePath);
    }

    @Bean
    @StepScope
    public UpdateComplexDealItemProcessor updateComplexDealItemProcessor(@Value("#{jobParameters[businessProcessId]}") Long businessProcessId) {
        return new UpdateComplexDealItemProcessor(businessProcessId);
    }

    @Bean
    @StepScope
    public UpdateComplexDealItemWriter updateComplexDealCsvItemWriter(@Value("#{jobParameters[sheetName]}") String sheetName, @Value("#{jobParameters[URL]}") String url) throws Exception {
        return new UpdateComplexDealItemWriter(sheetName, url);
    }

    public void putInJobExecutionContext(String key, Object value) {
        ExecutionContext stepExecutionContext = StepSynchronizationManager.getContext().getStepExecution().getJobExecution().getExecutionContext();
        stepExecutionContext.put(key, value);
    }

    @Bean
    @StepScope
    public UpdateEntityItemReader updateEntityItemReader(@Value("#{jobParameters[URL]}") String excelFilePath) throws IOException, CsvException {
        return new UpdateEntityItemReader(excelFilePath);
    }

    @Bean
    @StepScope
    public UpdateEntityItemProcessor updateEntityItemProcessor(@Value("#{jobParameters[entityType]}") String entityType, @Value("#{jobParameters[entityName]}") String entityName) {
        return new UpdateEntityItemProcessor(entityType, entityName);
    }

    @Bean
    public PartitionStepExecutionListener partitionStepExecutionListener() {
        return new PartitionStepExecutionListener();
    }

    @Bean
    @StepScope
    public PartitionedUpdateDealReader partitionedUpdateDealReader(
            @Value("#{jobParameters[URL]}") String url) throws IOException {
        return new PartitionedUpdateDealReader(url);
    }

    public TaskExecutor batchCustomTaskExecutor() {
        SimpleAsyncTaskExecutor taskExecutor = new SimpleAsyncTaskExecutor();
        taskExecutor.setConcurrencyLimit(3);
        return taskExecutor;
    }

    public TaskExecutor conservativeTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(15);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("Batch-Thread-");
        executor.initialize();
        return executor;
    }


    public int fetchConfigBasedOnConfigType(String configName) {
        Optional<OriginateBatchConfigurationEntity> originateBatchConfigurationEntity = this.originateBatchConfigurationRepository.findByConfigurationName("Batch_Properties");
        if (originateBatchConfigurationEntity.isPresent()) {
            OriginateBatchConfigurationEntity batchConfigurationEntity = originateBatchConfigurationEntity.get();
            JsonNode configDetails = batchConfigurationEntity.getConfigurationDetails();
            switch (configName) {
                case "chunkSize":
                    return configDetails.get("chunkSize").asInt();
                case "gridSize":
                    return configDetails.get("gridSize").asInt();
                case "maxPoolSize":
                    return configDetails.get("taskExecutor").get("maxPoolSize").asInt();
                case "corePoolSize":
                    return configDetails.get("taskExecutor").get("corePoolSize").asInt();
                case "queueCapacity":
                    return configDetails.get("taskExecutor").get("queueCapacity").asInt();
                case "throttleLimit":
                    return configDetails.get("taskExecutor").get("throttleLimit").asInt();
                case "partitionSize":
                    return configDetails.has("partitionSize") ?
                            configDetails.get("partitionSize").asInt() : 4;
                default:
                    return 0;
            }
        }
        return 0;
    }

    public String fetchPartitionBasedOnConfigType(String configName) {
        Optional<OriginateBatchConfigurationEntity> originateBatchConfigurationEntity = this.originateBatchConfigurationRepository.findByConfigurationName("Batch_Properties");
        if (originateBatchConfigurationEntity.isPresent()) {
            OriginateBatchConfigurationEntity batchConfigurationEntity = originateBatchConfigurationEntity.get();
            JsonNode configDetails = batchConfigurationEntity.getConfigurationDetails();
            if (configName.equals("partitionKey")) {
                return configDetails.get("partitionKey").asText();
            }
            return null;
        }
        return null;
    }

    @Bean
    public TaskExecutor batchPartitionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // Increase core pool size based on CPU cores
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("BatchPartition-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // Enable task decoration for better monitoring
        executor.setTaskDecorator(runnable -> {
            long startTime = System.currentTimeMillis();
            return () -> {
                try {
                    runnable.run();
                } finally {
                    long processingTime = System.currentTimeMillis() - startTime;
                    if (processingTime > 1000) { // Log slow tasks
                        System.out.println("Slow task detected: {" + processingTime + "} ms ");
                        logger.warn("Slow task detected: {}ms", processingTime);
                    }
                }
            };
        });
        executor.initialize();
        return executor;
    }

    @Bean
    public TaskExecutor updateDealTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("update-deal-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        // Use cached definition if available
        if (cachedBusinessProcessDefinition != null) {
            return cachedBusinessProcessDefinition;
        }

        Object businessProcessDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get("businessProcessDefinition");

        if (businessProcessDefinition instanceof BusinessProcessDefinitionEntity) {
            cachedBusinessProcessDefinition = (BusinessProcessDefinitionEntity) businessProcessDefinition;
            return cachedBusinessProcessDefinition;
        }

        return null;
    }

    private EntityDefinitionEntity getEntityDefinition() {
        if (entityDefinitionEntity != null) {
            return entityDefinitionEntity;
        }

        Object cachedDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get(ENTITY_DEFINITION);
        entityDefinitionEntity = (EntityDefinitionEntity) cachedDefinition;
        return entityDefinitionEntity;

    }
}


