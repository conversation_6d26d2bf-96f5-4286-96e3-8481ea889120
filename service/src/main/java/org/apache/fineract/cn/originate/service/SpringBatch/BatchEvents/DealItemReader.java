package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.slf4j.Logger;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;

import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.replaceSpecialCharacters;
import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.toCamelCase;


public class DealItemReader implements ItemReader<JsonNode> {

    private static final int MAX_RETRIES = 3;
    private final String csvFilePath;
    private Iterator<String[]> rowIterator;
    private String[] columnNames;
    private ArrayList<String> formattedColumnNames = new ArrayList<>();
    private EntityItemWriter csvItemWriter;
    private JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("originate-logger")
    private Logger logger;
    @Autowired
    private BatchUtils utils = new BatchUtils(jdbcTemplate);
    private boolean startProcessing = false;
    private volatile int processedRows = 0;


    public DealItemReader(String csvFilePath) throws IOException, CsvException {
        this.csvFilePath = csvFilePath;
        initialize();
    }

    private void initialize() throws IOException, CsvException {
        try (CSVReader csvReader = new CSVReader(new InputStreamReader(utils.streamFileFromLocal(csvFilePath)))) {
            List<String[]> rows = csvReader.readAll();
            if (!rows.isEmpty()) {
                columnNames = rows.get(0);
                formattedColumnNames.clear();
                for (String column : columnNames) {
                    String formattedColumn = toCamelCase(replaceSpecialCharacters(column));
                    formattedColumnNames.add(formattedColumn);
                }
                rowIterator = rows.subList(1, rows.size()).iterator();
                startProcessing = true;
            } else {
                throw new IllegalArgumentException("CSV file is empty");
            }
        } catch (Exception e) {
            startProcessing = false;
            throw new RuntimeException("Failed to initialize CSV reader: " + e.getMessage(), e);
        }
    }

    @Override
    public synchronized JsonNode read() throws IOException {
        logger.info("Inside Deal Item Reader - Processing row {}", processedRows + 1);

        if (rowIterator == null || !startProcessing) {
            logger.info("No more rows to process or processing not started");
            return null;
        }

        if (!rowIterator.hasNext()) {
            logger.info("Finished processing all rows. Total processed: {}", processedRows);
            return null;
        }

        try {
            String[] row = rowIterator.next();
            processedRows++;

            Map<String, Object> rowContent = new LinkedHashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();

            BusinessProcessDefinitionEntity businessProcessDefinitionEntity =
                    getBusinessProcessDefinition();

            if (businessProcessDefinitionEntity == null) {
                logger.error("Business Process Definition not found");
                return null;
            }

            processRow(row, rowContent, objectMapper, businessProcessDefinitionEntity);

            logger.info("Successfully processed row {}", objectMapper.valueToTree(rowContent));
            return objectMapper.valueToTree(rowContent);

        } catch (Exception e) {
            logger.error("Error processing row {}: {}", processedRows, e.getMessage());
            throw new RuntimeException("Failed to process row: " + e.getMessage(), e);
        }
    }

    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        Object businessProcessDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get("businessProcessDefinition");

        return (businessProcessDefinition instanceof BusinessProcessDefinitionEntity)
                ? (BusinessProcessDefinitionEntity) businessProcessDefinition
                : null;
    }

    private void processRow(String[] row, Map<String, Object> rowContent,
                            ObjectMapper objectMapper,
                            BusinessProcessDefinitionEntity businessProcessDefinitionEntity) {
        for (int i = 0; i < columnNames.length; i++) {
            String columnName = toCamelCase(replaceSpecialCharacters(columnNames[i]));
            String cellValue = (i < row.length && row[i] != null && !row[i].trim().isEmpty())
                    ? row[i].trim() : "";

            JsonNode columnNode = findColumnNode(businessProcessDefinitionEntity, columnName);
            if (columnNode == null) continue;

            processColumn(columnName, cellValue, rowContent, objectMapper, columnNode);
        }
    }

    private JsonNode findColumnNode(BusinessProcessDefinitionEntity entity, String columnName) {
        List<String> columnNameParts = Arrays.asList(columnName.split("~"));
        String rootColumn = columnNameParts.get(0);

        for (JsonNode node : entity.getAssetItems()) {
            if (node.has(rootColumn)) {
                return node.get(rootColumn);
            }
        }
        return null;
    }

    private void processColumn(String columnName, String cellValue,
                               Map<String, Object> rowContent,
                               ObjectMapper objectMapper, JsonNode columnNode) {
        JsonNode inputTypeNode = columnNode.get("inputType");
        if (inputTypeNode == null) {
            rowContent.put(columnName, cellValue);
            return;
        }

        String inputType = inputTypeNode.asText();
        try {
            if ("Searchable picklist".equals(inputType) ||
                    "Advance Picklist".equals(inputType)) {
                utils.handleSearchablePicklist(objectMapper, cellValue, rowContent, columnName);
            } else if ("Repetitive Section".equals(inputType)) {
                utils.handleRepetitiveSection(columnName, rowContent, objectMapper,
                        cellValue, columnNode);
            } else if ("Table".equals(inputType)) {
            } else {
                rowContent.put(columnName, cellValue);
            }
        } catch (Exception e) {
            logger.error("Error processing column {}: {}", columnName, e.getMessage());
            rowContent.put(columnName, cellValue);
        }
    }
}