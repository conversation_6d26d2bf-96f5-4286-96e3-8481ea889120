package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.replaceSpecialCharacters;
import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.toCamelCase;


public class EntityItemReader implements org.springframework.batch.item.ItemReader<JsonNode> {

    private final String csvFilePath;
    private Iterator<String[]> rowIterator;
    private String[] columnNames;
    private ArrayList<String> formattedColumnNames = new ArrayList<>();
    private EntityItemWriter csvItemWriter;
    private JdbcTemplate jdbcTemplate;

    private BatchUtils utils = new BatchUtils(jdbcTemplate);
    private boolean startProcessing = false;

    /**
     * Constructor that initializes the EntityItemReader with a CSV file path.
     * Sets up the reader and calls initialize() to prepare for reading CSV data.
     */
    public EntityItemReader(String csvFilePath) throws IOException, CsvException {
        this.csvFilePath = csvFilePath;
        initialize();
    }


    private void initialize() throws IOException, CsvException {
        try (CSVReader csvReader = new CSVReader(new InputStreamReader(utils.streamFileFromLocal(csvFilePath)))) {
            List<String[]> rows = csvReader.readAll();
            if (!rows.isEmpty()) {
                columnNames = rows.get(0);
                for (String column : columnNames) {
                    column = toCamelCase(replaceSpecialCharacters(column));
                    formattedColumnNames.add(column);
                }
                rowIterator = rows.subList(1, rows.size()).iterator();
                startProcessing = true;
            } else {
                throw new IllegalArgumentException("CSV file is empty");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public synchronized JsonNode read() throws IOException {
        if (rowIterator == null || !startProcessing) {
            return null;
        }

        if (rowIterator.hasNext()) {
            String[] row = rowIterator.next();

            Map<String, Object> rowContent = IntStream.range(0, columnNames.length)
                    .boxed()
                    .collect(Collectors.toMap(
                            i -> toCamelCase(replaceSpecialCharacters(columnNames[i])),
                            i -> (i < row.length && row[i] != null && !row[i].trim().isEmpty()) ? row[i].trim() : "",
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));

            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.valueToTree(rowContent);
        }

        return null;
    }

}
