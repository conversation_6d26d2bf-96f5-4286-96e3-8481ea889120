package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.fineract.cn.originate.api.v1.validation.ExceptionConstants.EntityConstants;
import org.apache.fineract.cn.originate.api.v1.validation.utils.Utility;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.EntityRowMapper;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.service.BusinessProcessEventService;
import org.apache.fineract.cn.originate.service.internal.service.EntityEventService;
import org.slf4j.Logger;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ItemProcessor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class UpdateEntityItemProcessor implements ItemProcessor<JsonNode, EntityJPA> {
    private static final String ENTITY_DEFINITION = "entityDefinition";
    private final String entityType;
    private final String entityName;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private BatchUtils batchUtils;
    @Autowired
    private EntityEventService entityEventService;
    @Autowired
    @Qualifier("originate-logger")
    private Logger logger;
    @Autowired
    private EntityDefinitionRepository entityDefinitionRepository;
    private EntityDefinitionEntity entityDefinitionEntity;

    public UpdateEntityItemProcessor(String entityType, String entityName) {
        this.entityType = entityType;
        this.entityName = entityName;
    }

    @Override
    public EntityJPA process(JsonNode jsonNode) throws Exception {
        EntityJPA entity = new EntityJPA();
        Map<String, JsonNode> map = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        EntityDefinitionEntity entityDefinition = getEntityDefinition();
        Utility.convertJsonNodeToMap(jsonNode.get("customerDetails").get(EntityConstants.ENTITY_DETAIL), map, true);
        ArrayList entityKey = batchUtils.getConfigValue(entityName, "Update_entity_keys");
        ArrayList<EntityJPA> entityList = fetchEntityBasedOnKeysAndExtension(jsonNode.get("customerDetails").get(EntityConstants.ENTITY_DETAIL).get(0), entityKey, (entityDefinition.getId()));

        for (EntityJPA entityJPA : entityList) {
            entityJPA.setModifiedBy("FINNATE");
            entityJPA.setModifiedDate(java.time.LocalDateTime.now());
            JsonNode customerDomainEntityDetail = mapper.valueToTree(map);
            JsonNode customerDetails = BusinessProcessEventService.merge(entityJPA.getCustomerDetails(), customerDomainEntityDetail);
            entityJPA.setCustomerDetails(customerDetails);
            entity = entityJPA;
        }
        return entity;
    }

    private ArrayList<EntityJPA> fetchEntityBasedOnKeysAndExtension(JsonNode node, ArrayList<String> fieldKeys, Long entityId) {
        ArrayList<EntityJPA> entityJPAArrayList = new ArrayList<>();
        if (fieldKeys == null || fieldKeys.isEmpty()) {
            return entityJPAArrayList;
        }

        StringBuilder sqlBuilder = new StringBuilder(
                " SELECT x.* FROM public.originate_entity x WHERE x.entity_id = ? AND x.entity_details_obj ->> ? = ? "
        );

        ArrayList<Object> params = new ArrayList<>();
        params.add(entityId);

        String fieldKey = fieldKeys.get(0);
        if (node.has(fieldKey)) {
            String value = node.get(fieldKey).get("value").asText();
            params.add(fieldKey);
            params.add(value);
        } else {
            logger.info("Field key '{}' not found in node", fieldKey);
            return entityJPAArrayList;
        }

        String sql = sqlBuilder.toString();
        logger.debug("Executing SQL: {} with params: {}", sql, params);
        entityJPAArrayList = (ArrayList<EntityJPA>) jdbcTemplate.query(sql, new EntityRowMapper<>(), params.toArray());

        return entityJPAArrayList;
    }


    private EntityDefinitionEntity getEntityDefinition() {
        if (entityDefinitionEntity != null) {
            return entityDefinitionEntity;
        }

        Object cachedDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get(ENTITY_DEFINITION);
        entityDefinitionEntity = (EntityDefinitionEntity) cachedDefinition;
        return entityDefinitionEntity;

    }

}
