package org.apache.fineract.cn.originate.service.SpringBatch.Handler;

import com.centelon.finnate.workflowengine.api.v1.events.WorkflowEngineSampleEventConstants;
import org.apache.fineract.cn.command.annotation.Aggregate;
import org.apache.fineract.cn.command.annotation.CommandHandler;
import org.apache.fineract.cn.command.annotation.CommandLogLevel;
import org.apache.fineract.cn.command.annotation.EventEmitter;
import org.apache.fineract.cn.originate.service.OriginateConfiguration;
import org.apache.fineract.cn.originate.service.SpringBatch.Command.UpdateEntityCsvBatchCommand;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.EntityBatchDomain;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.validation.Valid;
import java.time.Clock;
import java.time.LocalDateTime;

/**
 * Command handler for entity CSV batch update operations.
 * Manages the execution of entity update batch jobs including parameter validation,
 * job launching, and execution tracking for entity update workflows.
 */
@Aggregate
public class UpdateEntityCsvBatchHandler {
    private final OriginateConfiguration dataSourceConfiguration;
    private final JobRegistry jobRegistry;
    @Autowired
    @Qualifier("entityCsvBatchUpdateJob")
    private Job entityCsvBatchUpdateJob;
    @Autowired
    @Qualifier("asyncJobLauncher")
    private JobLauncher asyncJobLauncher;
    private JobExecution jobExecution;

    /**
     * Constructor that initializes the UpdateEntityCsvBatchHandler with configuration and job registry.
     * Sets up the handler for managing entity update batch job execution with data source configuration
     * and provides access to registered batch jobs.
     */
    @Autowired
    public UpdateEntityCsvBatchHandler(OriginateConfiguration dataSourceConfiguration, JobRegistry jobRegistry) {

        this.dataSourceConfiguration = dataSourceConfiguration;
        this.jobRegistry = jobRegistry;
    }

    /**
     * Main command handler method for processing entity update CSV batch commands.
     * Validates domain data, extracts entity parameters, builds job parameters with URL and entity info,
     * launches the entity update batch job asynchronously, and returns the job execution ID for tracking.
     */
    @CommandHandler(logStart = CommandLogLevel.INFO, logFinish = CommandLogLevel.INFO)
    @EventEmitter(selectorName = WorkflowEngineSampleEventConstants.SELECTOR_NAME, selectorValue = WorkflowEngineSampleEventConstants.BATCH)
    public Long alzHandler(@Valid final UpdateEntityCsvBatchCommand updateEntityCsvBatchCommand) throws Exception {
        EntityBatchDomain domainData = updateEntityCsvBatchCommand.getDomain();
        if (
                domainData.getUrl() == null ||
                        domainData.getEntityType() == null) {
            throw new IllegalArgumentException("Invalid batch domain data");
        } else {

            JobParameters jobParameters = new JobParametersBuilder().addLong("time", System.currentTimeMillis())
                    .addString("URL", domainData.getUrl())
                    .addString("timeStamp", LocalDateTime.now(Clock.systemUTC()).toString())
                    .addString("createdBy", "FINNATE")
                    .addString("entityType", domainData.getEntityType())
                    .addString("entityName", domainData.getEntityName())
                    .addString("entitySubType", domainData.getEntitySubType())
                    .toJobParameters();
            jobExecution = asyncJobLauncher.run(entityCsvBatchUpdateJob, jobParameters);
            return jobExecution.getJobId();
        }
    }
}
