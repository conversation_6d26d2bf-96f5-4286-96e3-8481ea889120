package org.apache.fineract.cn.originate.service.SpringBatch.Controller;


import org.apache.fineract.cn.anubis.annotation.AcceptedTokenType;
import org.apache.fineract.cn.anubis.annotation.Permittable;
import org.apache.fineract.cn.command.domain.CommandCallback;
import org.apache.fineract.cn.command.domain.CommandProcessingException;
import org.apache.fineract.cn.command.gateway.CommandGateway;
import org.apache.fineract.cn.lang.ServiceException;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.BusinessProcess;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchDomain.BatchStatusDomain;
import org.apache.fineract.cn.originate.service.SpringBatch.Command.*;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.*;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.service.BusinessProcessDefinitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * REST controller for batch processing operations including CSV uploads and job management.
 * Provides endpoints for entity and deal batch processing, status monitoring,
 * and configuration management for batch operations.
 */
@RestController
@RequestMapping("batch/upload")
public class BatchRestController {
    private final CommandGateway commandGateway;
    private final BatchUtils batchUtils;
    private final BusinessProcessDefinitionService businessProcessDefinitionService;
    @Autowired
    private OriginateBatchConfigurationRepository originateBatchConfigurationRepository;


    /**
     * Constructor that initializes the BatchRestController with required dependencies.
     * Sets up command gateway for processing batch commands, batch utilities for operations,
     * and business process service for validation and configuration.
     */
    @Autowired
    public BatchRestController(CommandGateway commandGateway, BatchUtils batchUtils, BusinessProcessDefinitionService businessProcessDefinitionService) {
        this.commandGateway = commandGateway;
        this.batchUtils = batchUtils;
        this.businessProcessDefinitionService = businessProcessDefinitionService;
    }

    /**
     * Processes entity batch operations from CSV uploads.
     * Creates and executes batch jobs for entity creation and processing,
     * returning the job execution ID for monitoring progress.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @RequestMapping(
            value = "/entity",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ResponseBody

    public ResponseEntity entityBatchProcess(@RequestBody EntityBatchDomain domain) throws ExecutionException, InterruptedException {
        CommandCallback<Long> ret;
        try {
            ret = this.commandGateway.process(new EntityCsvBatchCommand(domain), Long.class);
        } catch (CommandProcessingException e) {
            throw ServiceException.internalError("Command Processing Error" + e.toString());
        }
        return new ResponseEntity<>(ret.get(), HttpStatus.ACCEPTED);
    }

    /**
     * Processes deal batch operations from CSV uploads.
     * Creates and executes batch jobs for deal creation and processing,
     * returning the job execution ID for monitoring progress.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @RequestMapping(
            value = "/deal",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ResponseBody

    public ResponseEntity dealBatchProcess(@RequestBody DealBatchDomain domain) throws ExecutionException, InterruptedException {
        CommandCallback<Long> ret;
        try {
            ret = this.commandGateway.process(new DealCsvBatchCommand(domain), Long.class);
        } catch (CommandProcessingException e) {
            throw ServiceException.internalError("Command Processing Error" + e.toString());
        }
        return new ResponseEntity<>(ret.get(), HttpStatus.ACCEPTED);
    }

    /**
     * Processes complex deal batch update operations from CSV uploads.
     * Validates business process existence, sets process name, and executes
     * complex batch update jobs with advanced field processing and merging.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @RequestMapping(
            value = "/deal/update/complex",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ResponseBody

    public ResponseEntity updateComplexDealBatchProcess(@RequestBody DealBatchDomain domain) throws ExecutionException, InterruptedException {
        CommandCallback<Long> ret;
        try {
            Optional<BusinessProcess> businessProcess = this.businessProcessDefinitionService.findBusinessProcessById(domain.getBusinessProcessId());
            if (businessProcess.isPresent()) {
                domain.setBusinessProcessName(businessProcess.get().getName());
                ret = this.commandGateway.process(new UpdateComplexDealCsvBatchCommand(domain), Long.class);
            } else {
                return new ResponseEntity<>("Invalid Business Process Id or Business Process Does not Exist", HttpStatus.BAD_REQUEST);
            }
        } catch (CommandProcessingException e) {
            throw ServiceException.internalError("Command Processing Error" + e.toString());
        }
        return new ResponseEntity<>(ret.get(), HttpStatus.ACCEPTED);

    }

    /**
     * Processes standard deal batch update operations from CSV uploads.
     * Validates business process existence, sets process name, and executes
     * batch update jobs for existing deal entities with standard field processing.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @RequestMapping(
            value = "/deal/update",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ResponseBody

    public ResponseEntity updateDealBatchProcess(@RequestBody DealBatchDomain domain) throws ExecutionException, InterruptedException {
        CommandCallback<Long> ret;
        try {
            Optional<BusinessProcess> businessProcess = this.businessProcessDefinitionService.findBusinessProcessById(domain.getBusinessProcessId());
            if (businessProcess.isPresent()) {
                domain.setBusinessProcessName(businessProcess.get().getName());
                ret = this.commandGateway.process(new UpdateDealCsvBatchCommand(domain), Long.class);
            } else {
                return new ResponseEntity<>("Invalid Business Process Id or Business Process Does not Exist", HttpStatus.BAD_REQUEST);
            }
        } catch (CommandProcessingException e) {
            throw ServiceException.internalError("Command Processing Error" + e.toString());
        }
        return new ResponseEntity<>(ret.get(), HttpStatus.ACCEPTED);

    }

    /**
     * Processes entity batch update operations from CSV uploads.
     * Creates and executes batch jobs for updating existing entity objects,
     * returning the job execution ID for monitoring progress.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @RequestMapping(
            value = "/entity/update",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ResponseBody

    public ResponseEntity updateEntityBatchProcess(@RequestBody EntityBatchDomain domain) throws ExecutionException, InterruptedException {
        CommandCallback<Long> ret;
        try {
            ret = this.commandGateway.process(new UpdateEntityCsvBatchCommand(domain), Long.class);
        } catch (CommandProcessingException e) {
            throw ServiceException.internalError("Command Processing Error" + e.toString());
        }
        return new ResponseEntity<>(ret.get(), HttpStatus.ACCEPTED);
    }

    /**
     * Retrieves batch job execution status by job ID.
     * Provides detailed status information including step execution details,
     * timing, counts, and error information for monitoring batch job progress.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @RequestMapping(
            value = "/status/{id}",
            method = RequestMethod.GET,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ResponseBody
    public ResponseEntity<?> batchStatusBasedOnJobID(@PathVariable("id") Long id) {
        BatchStatusDomain batchStatus = batchUtils.getStatusById(id);
        if (batchStatus == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("No batch status found for ID: " + id);
        }
        return new ResponseEntity<>(batchStatus, HttpStatus.OK);
    }

    /**
     * Creates new batch configuration settings.
     * Accepts configuration details and metadata, creates a new configuration entity,
     * and persists it to the database for use in batch processing operations.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @PostMapping("/configuration/create")
    public ResponseEntity<?> createBatchConfig(@RequestBody OriginateBatchConfigurationDTO dto) {
        OriginateBatchConfigurationEntity entity = new OriginateBatchConfigurationEntity();
        entity.setConfigurationName(dto.getConfigurationName());
        entity.setConfigurationDetails(dto.getConfigurationDetails());
        entity.setCreatedBy(dto.getCreatedBy());
        entity.setModifiedBy(dto.getModifiedBy());
        entity.setCreatedDate(dto.getCreatedDate());
        entity.setModifiedDate(dto.getModifiedDate());
        this.originateBatchConfigurationRepository.save(entity);
        return new ResponseEntity<>("Configuration Created", HttpStatus.CREATED);

    }

    /**
     * Retrieves all batch configuration settings.
     * Returns a list of all available batch configurations for
     * management and selection purposes in batch operations.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @GetMapping("/configuration")
    public ResponseEntity<?> getAllBatchConfig() {

        return new ResponseEntity<>(this.originateBatchConfigurationRepository.findAll(), HttpStatus.OK);

    }

    /**
     * Retrieves a specific batch configuration by ID.
     * Returns the configuration details for a specific configuration
     * identified by its unique ID for viewing or editing purposes.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @GetMapping("/configuration/{id}")
    public ResponseEntity<?> getBatchConfigById(@PathVariable("id") Long Id) {

        return new ResponseEntity<>(this.originateBatchConfigurationRepository.findOne(Id), HttpStatus.OK);

    }

    /**
     * Updates an existing batch configuration.
     * Retrieves the existing configuration by ID, updates the configuration details
     * with new values, and persists the changes to the database.
     */
    @Permittable(value = AcceptedTokenType.GUEST)
    @PutMapping("/configuration/update")
    public ResponseEntity<?> updateBatchConfiguration(@RequestBody OriginateBatchConfigurationDTO dto) {

        OriginateBatchConfigurationEntity dtoFromRequest = this.originateBatchConfigurationRepository.findOne(dto.getId());

        dtoFromRequest.setConfigurationDetails(dto.getConfigurationDetails());
        this.originateBatchConfigurationRepository.save(dtoFromRequest);

        return new ResponseEntity<>("Configuration Updated", HttpStatus.CREATED);

    }

}
