package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.fineract.cn.lang.TenantContextHolder;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.WorkflowEngineResponse;
import org.apache.fineract.cn.originate.api.v1.domain.customer.Customer;
import org.apache.fineract.cn.originate.api.v1.domain.entity.enums.EntityEventEnums;
import org.apache.fineract.cn.originate.api.v1.validation.ExceptionConstants.EntityConstants;
import org.apache.fineract.cn.originate.api.v1.validation.utils.Utility;
import org.apache.fineract.cn.originate.service.internal.mapper.EntityMapper;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.service.BusinessProcessEventService;
import org.apache.fineract.cn.originate.service.internal.service.EntityEventService;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Clock;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.transformJson;

//TODO: To Discuss WRT rule execution while on create event
public class EntityItemProcessor implements ItemProcessor<JsonNode, EntityJPA> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String entityType;
    private final String entityName;
    @Autowired
    private EntityEventService entityEventService;
    @Autowired
    private EntityDefinitionRepository entityDefinitionRepository;
    public EntityItemProcessor(String entityType, String entityName) {
        this.entityType = entityType;
        this.entityName = entityName;
    }

    @Override
    public EntityJPA process(JsonNode node) {
        EntityDefinitionEntity entityDefinitionEntity = new EntityDefinitionEntity();
        try {
            Object entityDefinition = StepSynchronizationManager.getContext()
                    .getStepExecution()
                    .getJobExecution()
                    .getExecutionContext()
                    .get("entityDefinition");
            if (entityDefinition instanceof EntityDefinitionEntity) {
                entityDefinitionEntity = (EntityDefinitionEntity) entityDefinition;
            } else {
                Optional<EntityDefinitionEntity> optionalEntityDefinitionEntity = entityDefinitionRepository.findFirstByEntityNameAndEntityTypeOrderByVersionDesc(entityName, entityType);
                if (!optionalEntityDefinitionEntity.isPresent()) {
                    throw new IllegalArgumentException("Entity definition not found for entityType: " + entityType + " and entityName: " + entityName);
                } else {
                    entityDefinitionEntity = optionalEntityDefinitionEntity.get();
                    putInJobExecutionContext("entityDefinition", entityDefinitionEntity);
                }
            }
            Map<String, JsonNode> map = new HashMap<>();
            ObjectMapper mapper = new ObjectMapper();
            if (!(node instanceof ObjectNode)) {
                throw new IllegalArgumentException("Expected item to be an ObjectNode");
            }
            Customer customer = new Customer();
            ObjectNode outputJson = transformJson(node, mapper,"entityDetail");
            Utility.convertJsonNodeToMap(outputJson.get(EntityConstants.ENTITY_DETAIL), map, true);
            JsonNode customerDomainEntityDetail = mapper.valueToTree(map);
            customer.setName(
                    (node.hasNonNull("customerName") && !node.get("customerName").asText().isEmpty())
                            ? node.get("customerName").asText()
                            : entityDefinitionEntity.getEntityName()
            );
            customer.setEntityId(entityDefinitionEntity.getId());
            customer.setEntityType(entityDefinitionEntity.getEntityType());
            customer.setCustomerDetails(BusinessProcessEventService.merge(entityDefinitionEntity.getEntityDetail(), customerDomainEntityDetail));
            WorkflowEngineResponse workflowEngineResponse;
            workflowEngineResponse = this.entityEventService.executeOnEvent(customer, EntityEventEnums.CREATE.getEvent(), null, null);
            if (workflowEngineResponse.getErrorList() != null && workflowEngineResponse.getIsErrorpresent().equals(true)) {
                throw new RuntimeException(String.valueOf(workflowEngineResponse.getErrorList()));
            }
            final EntityJPA customerEntity = EntityMapper.map(customer);
            customerEntity.setEntityDefinitionEntity(entityDefinitionEntity);
            customerEntity.setCreatedBy(TenantContextHolder.checkedGetIdentifier());
            customerEntity.setCreatedDate(LocalDateTime.now(Clock.systemUTC()));
            return customerEntity;
        } catch (Exception e) {
            throw new RuntimeException("Failed to process json node", e);
        }
    }

    public void putInJobExecutionContext(String key, Object value) {
        ExecutionContext stepExecutionContext = StepSynchronizationManager.getContext().getStepExecution().getJobExecution().getExecutionContext();
        stepExecutionContext.put(key, value);
    }
}
