package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.time.Duration;
import java.time.LocalDateTime;

public class EntityJobCompletionNotificationListener implements JobExecutionListener {

    private static final Logger logger = LoggerFactory.getLogger(EntityJobCompletionNotificationListener.class);
    private LocalDateTime startTime;

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    @Qualifier("entityCsvBatchInsertJob")
    private Job yourJob;

    /**
     * Executes before the job starts to initialize tracking and logging.
     * Records the start time and logs job initiation details including
     * the job name for monitoring and debugging purposes.
     */
    @Override
    public void beforeJob(JobExecution jobExecution) {
        startTime = LocalDateTime.now();
        logger.info("Job started at: {}, Job Name: {}", startTime, jobExecution.getJobInstance().getJobName());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        LocalDateTime endTime = LocalDateTime.now();
        Duration duration = Duration.between(startTime, endTime);

        logger.info("Job finished at: {}, Status: {}, Duration: {} seconds",
                endTime,
                jobExecution.getStatus(),
                duration.getSeconds());

        if (jobExecution.getStatus() == BatchStatus.FAILED || jobExecution.getStatus() == BatchStatus.STOPPED) {
            try {
                String jobName = jobExecution.getJobInstance().getJobName();
                logger.info("Job failed or stopped, attempting to restart job: {}", jobName);

                JobExecution lastExecution = jobRepository.getLastJobExecution(jobName, jobExecution.getJobParameters());

                if (lastExecution != null) {
                    JobParameters lastJobParameters = lastExecution.getJobParameters();
                    jobLauncher.run(yourJob, lastJobParameters);
                }
            } catch (Exception e) {
                logger.error("Error occurred while restarting job", e);
                throw new RuntimeException("Error Occurred ", e);
            }
        }
    }
}
