package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityRepository;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public class UpdateEntityItemWriter implements ItemWriter<EntityJPA> {
    @Autowired
    private EntityRepository entityRepository;

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW,
            rollbackFor = Exception.class)
    public void write(List<? extends EntityJPA> list) throws Exception {
        if (list.isEmpty()) {
            return;
        }
        entityRepository.save(list);
    }
}
