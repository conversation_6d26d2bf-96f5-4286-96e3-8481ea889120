package org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils;

import org.apache.fineract.cn.originate.service.internal.repository.deal.DealAssetEntity;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;

public class DealComposite {
    private final DealEntity dealEntity;
    private final DealAssetEntity dealAssetEntity;

    /**
     * Constructor that creates a composite object containing both deal and deal asset entities.
     * This composite pattern allows batch operations to handle related entities together
     * for atomic updates and consistent data processing.
     */
    public DealComposite(DealEntity dealEntity, DealAssetEntity dealAssetEntity) {
        this.dealEntity = dealEntity;
        this.dealAssetEntity = dealAssetEntity;
    }

    /**
     * Returns the deal entity component of this composite object.
     * Provides access to the main deal information including metadata,
     * status, and business process associations.
     */
    public DealEntity getDealEntity() {
        return dealEntity;
    }

    /**
     * Returns the deal asset entity component of this composite object.
     * Provides access to the deal's asset information including JSON data,
     * field values, and asset-specific configurations.
     */
    public DealAssetEntity getDealAssetEntity() {
        return dealAssetEntity;
    }
}

