package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.apache.fineract.cn.lang.ServiceException;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.IOException;
import java.io.InputStreamReader;

import java.util.*;


import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.replaceSpecialCharacters;
import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.toCamelCase;


public class UpdateDealItemReader implements ItemReader<JsonNode> {
    private static final Logger logger = LoggerFactory.getLogger(UpdateDealItemReader.class);
    private final String csvFilePath;
    private Iterator<String[]> rowIterator;
    private String[] columnNames;
    private List<String> formattedColumnNames = new ArrayList<>();
    private boolean startProcessing;
    private int processedRows = 0;
    private JdbcTemplate jdbcTemplate;

    private Map<String, Object> rowContent;
    @Autowired
    private BatchUtils utils = new BatchUtils(jdbcTemplate);
    @Autowired
    private EntityRepository entityRepository;

    /**
     * Constructor that initializes the UpdateDealItemReader with a CSV file path.
     * Sets up the reader for processing deal updates and calls initialize()
     * to prepare the CSV data for reading.
     */
    public UpdateDealItemReader(String csvFilePath) throws IOException, CsvException {
        this.csvFilePath = csvFilePath;
        this.rowContent = new LinkedHashMap<>();
        initialize();
    }

    /**
     * Initializes the CSV reader by loading the file, extracting headers, and preparing for processing.
     * Formats column names to camelCase and sets up the row iterator for sequential reading.
     * Validates that the CSV file is not empty before proceeding.
     */
    private void initialize() throws IOException, CsvException {
        try (CSVReader csvReader = new CSVReader(new InputStreamReader(utils.streamFileFromLocal(csvFilePath)))) {
            List<String[]> rows = csvReader.readAll();
            if (!rows.isEmpty()) {
                columnNames = rows.get(0);
                formattedColumnNames.clear();
                for (String column : columnNames) {
                    String formattedColumn = toCamelCase(replaceSpecialCharacters(column));
                    formattedColumnNames.add(formattedColumn);
                }
                rowIterator = rows.subList(1, rows.size()).iterator();
                startProcessing = true;
            } else {
                throw new IllegalArgumentException("CSV file is empty");
            }
        } catch (Exception e) {
            startProcessing = false;
            throw new RuntimeException("Failed to initialize CSV reader: " + e.getMessage(), e);
        }
    }

    /**
     * Main reading method that processes CSV rows sequentially for deal updates.
     * Converts each row to JsonNode format with business process field mapping,
     * returning null when all rows have been processed.
     */
    @Override
    public JsonNode read() throws IOException {
        logger.info("Inside Deal Item Reader - Processing row {}", processedRows + 1);
        if (rowIterator == null || !startProcessing) {
            logger.info("No more rows to process or processing not started");
            return null;
        }

        if (!rowIterator.hasNext()) {
            logger.info("Finished processing all rows. Total processed: {}", processedRows);
            return null;
        }

        try {
            String[] row = rowIterator.next();
            processedRows++;

            Map<String, Object> rowContent = new LinkedHashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();

            BusinessProcessDefinitionEntity businessProcessDefinitionEntity =
                    getBusinessProcessDefinition();

            if (businessProcessDefinitionEntity == null) {
                logger.error("Business Process Definition not found");
                return null;
            }

            processRow(row, rowContent, objectMapper, businessProcessDefinitionEntity);

            logger.info("Successfully processed row {}", objectMapper.valueToTree(rowContent));
            return objectMapper.valueToTree(rowContent);

        } catch (Exception e) {
            logger.error("Error processing row {}: {}", processedRows, e.getMessage());
            throw new RuntimeException("Failed to process row: " + e.getMessage(), e);
        }
    }

    /**
     * Retrieves the business process definition from the job execution context.
     * This method accesses cached business process definition data that was
     * stored during job initialization to avoid repeated database lookups.
     */
    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        Object businessProcessDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get("businessProcessDefinition");

        return (businessProcessDefinition instanceof BusinessProcessDefinitionEntity)
                ? (BusinessProcessDefinitionEntity) businessProcessDefinition
                : null;
    }

    /**
     * Processes a single CSV row by mapping column values to business process field definitions.
     * Handles different input types including repetitive sections, searchable picklists,
     * and nested structures with parent~child relationships.
     */
    private void processRow(String[] row, Map<String, Object> rowContent,
                            ObjectMapper objectMapper,
                            BusinessProcessDefinitionEntity businessProcessDefinitionEntity) {
        // Create a map of column names to their values from the CSV
        Map<String, String> csvValues = new HashMap<>();
        for (int i = 0; i < Math.min(columnNames.length, row.length); i++) {
            String columnName = formattedColumnNames.get(i);
            String cellValue = (row[i] != null && !row[i].trim().isEmpty()) ? row[i].trim() : "";
            csvValues.put(columnName, cellValue);
        }

        // Extract field definitions from business process definition
        JsonNode fieldDefinitions = businessProcessDefinitionEntity.getAssetItems();

        // Identify repetitive sections and their fields
        Map<String, Set<String>> repetitiveSectionFields = new HashMap<>();
        Map<String, JsonNode> repetitiveSectionDefinitions = new HashMap<>();
        Map<String, String> fieldTypes = new HashMap<>();

        // First pass: identify all repetitive sections and their fields, and collect field types
        for (JsonNode fieldDef : fieldDefinitions) {
            Iterator<String> fieldNames = fieldDef.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode fieldNode = fieldDef.get(fieldName);

                if (fieldNode.has("inputType")) {
                    String inputType = fieldNode.get("inputType").asText();
                    fieldTypes.put(fieldName, inputType);

                    if ("Repetitive Section".equals(inputType)) {
                        repetitiveSectionDefinitions.put(fieldName, fieldNode);
                        repetitiveSectionFields.put(fieldName, new HashSet<>());

                        // Extract fields from the repetitive section definition
                        if (fieldNode.has("displayProperty") &&
                                fieldNode.get("displayProperty").has("defaultValues") &&
                                fieldNode.get("displayProperty").get("defaultValues").isArray()) {

                            JsonNode defaultValues = fieldNode.get("displayProperty").get("defaultValues");
                            for (JsonNode itemDef : defaultValues) {
                                Iterator<String> itemFieldNames = itemDef.fieldNames();
                                while (itemFieldNames.hasNext()) {
                                    String itemFieldName = itemFieldNames.next();
                                    repetitiveSectionFields.get(fieldName).add(itemFieldName);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Second pass: process regular fields and repetitive sections
        Set<String> processedFields = new HashSet<>();

        for (String columnName : csvValues.keySet()) {
            if (!processedFields.contains(columnName)) {
                String cellValue = csvValues.get(columnName);
                String fieldType = fieldTypes.getOrDefault(columnName, "");

                // Handle special field types
                if ("Searchable picklist".equals(fieldType) || "Advance Picklist".equals(fieldType)) {
                    ObjectNode specialPicklistNode = objectMapper.createObjectNode();
                    List<EntityJPA> jpa = entityRepository.findByName(cellValue);
                    if (!jpa.isEmpty()) {
                        specialPicklistNode.put("id", jpa.get(0).getId());
                    } else {
                        specialPicklistNode.put("id", 1234);
                    }
                    specialPicklistNode.put("name", cellValue);
                    specialPicklistNode.putArray("details");

                    // Create wrapper with value node for consistency
                    ObjectNode wrapper = objectMapper.createObjectNode();
                    wrapper.set("value", specialPicklistNode);
                    rowContent.put(columnName, wrapper);
                } else {
                    rowContent.put(columnName, cellValue);
                }
            }
        }
        Map<String, Map<String, List<String>>> groupedRepetitiveValues = new HashMap<>();

// Group values by parent~child structure
        for (String columnName : csvValues.keySet()) {
            if (columnName.contains("~")) {
                String[] parts = columnName.split("~");
                String parent = parts[0];
                String child = parts[1];

                String cellValue = csvValues.get(columnName);
                List<String> values = Arrays.asList(cellValue.split("\\s*,\\s*")); // split by comma and trim

                groupedRepetitiveValues
                        .computeIfAbsent(parent, k -> new HashMap<>())
                        .put(child, values);
            }
        }

// Construct nested JSON arrays for each grouped parent
        for (String parent : groupedRepetitiveValues.keySet()) {
            Map<String, List<String>> children = groupedRepetitiveValues.get(parent);
            int numItems = children.values().stream().mapToInt(List::size).max().orElse(0);

            ArrayNode arrayNode = objectMapper.createArrayNode();

            for (int i = 0; i < numItems; i++) {
                ObjectNode itemNode = objectMapper.createObjectNode();
                for (Map.Entry<String, List<String>> entry : children.entrySet()) {
                    String childKey = entry.getKey();
                    List<String> childValues = entry.getValue();
                    String value = i < childValues.size() ? childValues.get(i) : ""; // fallback if missing
                    itemNode.put(childKey, value);
                }
                arrayNode.add(itemNode);
            }

            // Add to rowContent as an object with "value" key (to match your existing structure)
            ObjectNode parentNode = objectMapper.createObjectNode();
            parentNode.set("value", arrayNode);
            rowContent.put(parent, parentNode);
        }

    }

    /**
     * Recursively applies CSV values to a JSON template structure.
     * Traverses object and array nodes, replacing template placeholders
     * with actual values from the CSV data while preserving structure.
     */
    private JsonNode applyValuesToTemplate(JsonNode template, Map<String, String> values, ObjectMapper mapper) {
        if (template.isObject()) {
            ObjectNode result = mapper.createObjectNode();
            template.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode valueNode = entry.getValue();

                if (valueNode.isTextual() && values.containsKey(valueNode.asText())) {
                    // If template value matches a column name, replace with actual value
                    result.put(key, values.get(valueNode.asText()));
                } else if (valueNode.isArray() || valueNode.isObject()) {
                    // Recursively process nested structures
                    result.set(key, applyValuesToTemplate(valueNode, values, mapper));
                } else {
                    // Keep original value
                    result.set(key, valueNode.deepCopy());
                }
            });
            return result;
        } else if (template.isArray()) {
            return processArrayNode(template, values, mapper);
        } else {
            return template;
        }
    }

    /**
     * Processes array nodes within JSON templates by applying values to each array element.
     * Iterates through array items and recursively applies template value substitution
     * to maintain array structure while populating with CSV data.
     */
    private JsonNode processArrayNode(JsonNode arrayNode, Map<String, String> values, ObjectMapper mapper) {
        ArrayNode arrayResult = mapper.createArrayNode();
        for (JsonNode item : arrayNode) {
            arrayResult.add(applyValuesToTemplate(item, values, mapper));
        }
        return arrayResult;
    }
}
