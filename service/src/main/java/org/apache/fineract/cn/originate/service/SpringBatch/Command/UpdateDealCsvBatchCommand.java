package org.apache.fineract.cn.originate.service.SpringBatch.Command;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.DealBatchDomain;

/**
 * Command class for standard deal CSV batch update operations.
 * Encapsulates the deal batch domain data required for executing
 * batch update operations for existing deal entities.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateDealCsvBatchCommand {
    private DealBatchDomain dealBatchDomain;
}
