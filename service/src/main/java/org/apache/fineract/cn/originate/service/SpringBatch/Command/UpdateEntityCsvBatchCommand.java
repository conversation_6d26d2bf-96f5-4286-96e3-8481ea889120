package org.apache.fineract.cn.originate.service.SpringBatch.Command;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.EntityBatchDomain;

/**
 * Command class for entity CSV batch update operations.
 * Encapsulates the entity batch domain data required for executing
 * batch update operations for existing entity objects.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateEntityCsvBatchCommand {
    private EntityBatchDomain domain;
}
