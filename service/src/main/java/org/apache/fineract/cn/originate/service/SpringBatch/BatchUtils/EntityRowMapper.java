package org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.fineract.cn.originate.api.v1.domain.deal.*;
import org.apache.fineract.cn.originate.api.v1.domain.entity.CustomerBasicDetails;
import org.apache.fineract.cn.originate.api.v1.domain.reqdoc.DocumentDomain;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.entity.EntityDefinitionEntity;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.jdbc.core.RowMapper;
import org.postgresql.util.PGobject;

import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

public class EntityRowMapper<D> implements RowMapper<EntityJPA> {

    private static final String ENTITY_DEFINITION = "entityDefinition";
    private final ObjectMapper objectMapper = new ObjectMapper();
    private EntityDefinitionEntity entityDefinitionEntity;

    @Override
    public EntityJPA mapRow(ResultSet rs, int rowNum) throws SQLException {
        EntityJPA entityJPA = new EntityJPA();
        try {
            entityJPA.setId(rs.getLong("customer_id"));
            entityJPA.setName(rs.getString("name"));
            entityJPA.setCreatedDate(
                    rs.getDate("created_date") != null
                            ? rs.getDate("created_date").toLocalDate().atStartOfDay()
                            : null
            );

            entityJPA.setCreatedBy(rs.getString("created_by"));
            entityJPA.setEntityDefinitionEntity(getEntityDefinition());
            entityJPA.setEntityType(rs.getString("entity_type"));


            // Handle customerDetails as JsonNode first
            JsonNode customerDetails = readJson(rs, "customer_details", JsonNode.class);
            if (customerDetails != null) {
                if (customerDetails.isArray()) {
                    // If it's an array, take the first element
                    if (customerDetails.size() > 0) {
                        entityJPA.setCustomerDetails(customerDetails);
                    }
                } else {
                    entityJPA.setCustomerDetails(customerDetails);
                }
            }

        } catch (Exception e) {
            throw new SQLException("Error mapping Deal object", e);
        }
        return entityJPA;
    }

    private <T> T readJson(ResultSet rs, String column, Class<T> valueType) throws SQLException {
        PGobject pgObject = (PGobject) rs.getObject(column);
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                String jsonValue = pgObject.getValue();
                // Configure ObjectMapper for more lenient parsing
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

                return objectMapper.readValue(jsonValue, valueType);
            } catch (IOException e) {
                throw new SQLException(
                        String.format("Failed to deserialize column %s into %s. JSON value: %s. Error: %s",
                                column,
                                valueType.getSimpleName(),
                                pgObject.getValue(),
                                e.getMessage()
                        ),
                        e
                );
            }
        }
        return null;
    }

    private <T> List<T> readJsonList(ResultSet rs, String column, Class<T> elementType) throws SQLException {
        PGobject pgObject = (PGobject) rs.getObject(column);
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                return objectMapper.readValue(pgObject.getValue(), objectMapper.getTypeFactory().constructCollectionType(List.class, elementType));
            } catch (IOException e) {
                throw new SQLException("Failed to deserialize column " + column + " into List<" + elementType.getSimpleName() + ">", e);
            }
        }
        return Collections.emptyList();
    }

    private JsonNode readJsonNode(ResultSet rs, String column) throws SQLException {
        PGobject pgObject = (PGobject) rs.getObject(column);
        if (pgObject != null && pgObject.getValue() != null) {
            try {
                return objectMapper.readTree(pgObject.getValue());
            } catch (IOException e) {
                throw new SQLException("Failed to deserialize column " + column + " into JsonNode", e);
            }
        }
        return null;
    }

    private EntityDefinitionEntity getEntityDefinition() {
        if (entityDefinitionEntity != null) {
            return entityDefinitionEntity;
        }

        Object cachedDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get(ENTITY_DEFINITION);
        entityDefinitionEntity = (EntityDefinitionEntity) cachedDefinition;
        return entityDefinitionEntity;

    }
}