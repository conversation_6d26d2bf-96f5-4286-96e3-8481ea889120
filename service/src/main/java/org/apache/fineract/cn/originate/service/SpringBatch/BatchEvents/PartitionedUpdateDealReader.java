package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityJPA;
import org.apache.fineract.cn.originate.service.internal.repository.customer.EntityRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class PartitionedUpdateDealReader implements ItemReader<JsonNode> {

    private static final Logger logger = LoggerFactory.getLogger(PartitionedUpdateDealReader.class);

    private final String filePath;
    private final AtomicInteger currentIndex = new AtomicInteger(0);
    private List<String[]> partitionRows;
    private String[] columnNames;
    private List<String> formattedColumnNames = new ArrayList<>();
    @Autowired
    private BatchUtils utils;

    @Autowired
    private EntityRepository entityRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;
    private PartitionType partitionType = PartitionType.ROW;
    private String scheduleId = "";
    private int columnIndex = -1;
    private List<Integer> rowIndices = new ArrayList<>();
    /**
     * Constructor that initializes the PartitionedUpdateDealReader with file path and partition configuration.
     * Determines partition type (ROW, ID, or EMPTY) from execution context and sets up
     * appropriate row indices for processing the assigned partition of CSV data.
     */
    public PartitionedUpdateDealReader(@Value("#{jobParameters[URL]}") String filePath) throws IOException {
        this.filePath = filePath;

        ExecutionContext executionContext = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getExecutionContext();

        String partitionType = executionContext.getString("partitionType", "ROW");

        if ("ROW".equals(partitionType)) {
            int minIndex = Integer.parseInt(executionContext.getString("minValue", "0"));
            int maxIndex = Integer.parseInt(executionContext.getString("maxValue", "0"));
            this.partitionType = PartitionType.ROW;

            for (int i = minIndex; i <= maxIndex; i++) {
                rowIndices.add(i);
            }

        } else if ("ID".equals(partitionType)) {
            this.partitionType = PartitionType.ID;
            this.scheduleId = executionContext.getString("ID", "");
            this.columnIndex = executionContext.getInt("columnIndex", -1);

            String rowIndicesStr = executionContext.getString("rowIndices", "");
            if (!rowIndicesStr.isEmpty()) {
                this.rowIndices = Arrays.stream(rowIndicesStr.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            }

            logger.info("Initialized reader for ScheduleId {}, processing {} rows", scheduleId, rowIndices.size());

        } else {
            this.partitionType = PartitionType.EMPTY;
        }

        try {
            initialize();
        } catch (CsvException e) {
            throw new RuntimeException("Failed to initialize CSV reader: " + e.getMessage(), e);
        }
    }

    /**
     * Initializes the CSV reader by loading the file, extracting headers, and preparing partition rows.
     * Formats column names to camelCase and loads only the rows assigned to this partition
     * based on the row indices determined during construction.
     */
    private void initialize() throws IOException, CsvException {
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            List<String[]> allRows = reader.readAll();

            if (allRows.isEmpty()) {
                logger.warn("CSV file is empty");
                partitionRows = new ArrayList<>(0);
                return;
            }

            columnNames = allRows.get(0);
            formattedColumnNames.clear();

            for (String column : columnNames) {
                String formattedColumn = toCamelCase(replaceSpecialCharacters(column));
                formattedColumnNames.add(formattedColumn);
            }

            partitionRows = new ArrayList<>();
            for (int rowIndex : rowIndices) {
                if (rowIndex > 0 && rowIndex < allRows.size()) {
                    partitionRows.add(allRows.get(rowIndex));
                }
            }

            logger.info("Initialized partitioned reader for {} partition, loaded {} records",
                    partitionType, partitionRows.size());
        }
    }

    /**
     * Main reading method that processes partition rows sequentially and converts them to JsonNode.
     * Handles both ROW and ID partition types, processes business logic for each row,
     * and returns null when all partition rows have been processed.
     */
    @Override
    public JsonNode read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        logger.info("Inside Partitioned Deal Item Reader - Processing row {}", currentIndex.get() + 1);

        int index = currentIndex.getAndIncrement();
        if (index >= partitionRows.size()) {
            logger.info("Finished processing all rows in partition. Total processed: {}", index);
            return null;
        }

        logger.info("Processing row {} for {} {}",
                index + 1,
                partitionType == PartitionType.ID ? "ScheduleId" : "partition",
                partitionType == PartitionType.ID ? scheduleId : index);

        try {
            String[] row = partitionRows.get(index);

            Map<String, Object> rowContent = new LinkedHashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();

            BusinessProcessDefinitionEntity businessProcessDefinitionEntity = getBusinessProcessDefinition();

            if (businessProcessDefinitionEntity == null) {
                logger.error("Business Process Definition not found");
                return null;
            }

            processRow(row, rowContent, objectMapper, businessProcessDefinitionEntity);

            if (partitionType == PartitionType.ID) {
                rowContent.put("scheduleId", scheduleId);
            }

            JsonNode result = objectMapper.valueToTree(rowContent);
            logger.debug("Successfully processed row {}", result);
            return result;

        } catch (Exception e) {
            logger.error("Error processing row {}: {}", index, e.getMessage());
            throw new RuntimeException("Failed to process row: " + e.getMessage(), e);
        }
    }

    /**
     * Retrieves the business process definition from the job execution context.
     * This method accesses cached business process definition data that was
     * stored during job initialization to avoid repeated database lookups.
     */
    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        Object businessProcessDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get("businessProcessDefinition");

        return (businessProcessDefinition instanceof BusinessProcessDefinitionEntity)
                ? (BusinessProcessDefinitionEntity) businessProcessDefinition
                : null;
    }

    /**
     * Processes a single CSV row by mapping column values to business process field definitions.
     * Handles different input types including repetitive sections and searchable picklists,
     * applying appropriate transformations based on field configuration.
     */
    private void processRow(String[] row, Map<String, Object> rowContent,
                            ObjectMapper objectMapper,
                            BusinessProcessDefinitionEntity businessProcessDefinitionEntity) {
        Map<String, String> csvValues = new HashMap<>();
        for (int i = 0; i < Math.min(columnNames.length, row.length); i++) {
            String columnName = formattedColumnNames.get(i);
            String cellValue = (row[i] != null && !row[i].trim().isEmpty()) ? row[i].trim() : "";
            csvValues.put(columnName, cellValue);
        }

        JsonNode fieldDefinitions = businessProcessDefinitionEntity.getAssetItems();

        Map<String, Set<String>> repetitiveSectionFields = new HashMap<>();
        Map<String, JsonNode> repetitiveSectionDefinitions = new HashMap<>();
        Map<String, String> fieldTypes = new HashMap<>();

        for (JsonNode fieldDef : fieldDefinitions) {
            Iterator<String> fieldNames = fieldDef.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode fieldNode = fieldDef.get(fieldName);

                if (fieldNode.has("inputType")) {
                    String inputType = fieldNode.get("inputType").asText();
                    fieldTypes.put(fieldName, inputType);

                    if ("Repetitive Section".equals(inputType)) {
                        repetitiveSectionDefinitions.put(fieldName, fieldNode);
                        repetitiveSectionFields.put(fieldName, new HashSet<>());

                        if (fieldNode.has("displayProperty") &&
                                fieldNode.get("displayProperty").has("defaultValues") &&
                                fieldNode.get("displayProperty").get("defaultValues").isArray()) {

                            JsonNode defaultValues = fieldNode.get("displayProperty").get("defaultValues");
                            for (JsonNode itemDef : defaultValues) {
                                Iterator<String> itemFieldNames = itemDef.fieldNames();
                                while (itemFieldNames.hasNext()) {
                                    String itemFieldName = itemFieldNames.next();
                                    repetitiveSectionFields.get(fieldName).add(itemFieldName);
                                }
                            }
                        }
                    }
                }
            }
        }

        Set<String> processedFields = new HashSet<>();

        for (String sectionName : repetitiveSectionDefinitions.keySet()) {

            Set<String> sectionFields = repetitiveSectionFields.get(sectionName);

            ArrayNode sectionArray = objectMapper.createArrayNode();

            ObjectNode sectionItem = objectMapper.createObjectNode();

            boolean hasValues = false;
            for (String sectionField : sectionFields) {
                for (String csvField : csvValues.keySet()) {
                    if (csvField.equals(sectionField)) {
                        sectionItem.put(sectionField, csvValues.get(csvField));
                        processedFields.add(csvField);
                        hasValues = true;
                    }
                }
            }

            if (hasValues) {
                sectionArray.add(sectionItem);

                ObjectNode sectionWrapper = objectMapper.createObjectNode();
                sectionWrapper.set("value", sectionArray);
                rowContent.put(sectionName, sectionWrapper);
            }
        }

        for (String columnName : csvValues.keySet()) {
            if (!processedFields.contains(columnName)) {
                String cellValue = csvValues.get(columnName);
                String fieldType = fieldTypes.getOrDefault(columnName, "");

                if ("Searchable picklist".equals(fieldType) || "Advance Picklist".equals(fieldType)) {
                    ObjectNode specialPicklistNode = objectMapper.createObjectNode();
                    List<EntityJPA> jpa = entityRepository.findByName(cellValue);
                    if (!jpa.isEmpty()) {
                        specialPicklistNode.put("id", jpa.get(0).getId());
                    } else {
                        specialPicklistNode.put("id", 1234);
                    }
                    specialPicklistNode.put("name", cellValue);
                    specialPicklistNode.putArray("details");

                    ObjectNode wrapper = objectMapper.createObjectNode();
                    wrapper.set("value", specialPicklistNode);
                    rowContent.put(columnName, wrapper);
                } else {
                    rowContent.put(columnName, cellValue);
                }
            }
        }
    }

    /**
     * Recursively applies CSV values to a JSON template structure.
     * Traverses object and array nodes, replacing template placeholders
     * with actual values from the CSV data while preserving structure.
     */
    private JsonNode applyValuesToTemplate(JsonNode template, Map<String, String> values, ObjectMapper mapper) {
        if (template.isObject()) {
            ObjectNode result = mapper.createObjectNode();
            template.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode valueNode = entry.getValue();

                if (valueNode.isTextual() && values.containsKey(valueNode.asText())) {
                    result.put(key, values.get(valueNode.asText()));
                } else if (valueNode.isArray() || valueNode.isObject()) {
                    result.set(key, applyValuesToTemplate(valueNode, values, mapper));
                } else {
                    result.set(key, valueNode.deepCopy());
                }
            });
            return result;
        } else if (template.isArray()) {
            return processArrayNode(template, values, mapper);
        } else {
            return template;
        }
    }

    /**
     * Processes array nodes within JSON templates by applying values to each array element.
     * Iterates through array items and recursively applies template value substitution
     * to maintain array structure while populating with CSV data.
     */
    private JsonNode processArrayNode(JsonNode arrayNode, Map<String, String> values, ObjectMapper mapper) {
        ArrayNode arrayResult = mapper.createArrayNode();
        for (JsonNode item : arrayNode) {
            arrayResult.add(applyValuesToTemplate(item, values, mapper));
        }
        return arrayResult;
    }

    /**
     * Utility method that removes special characters from input strings.
     * Replaces all non-alphanumeric characters with spaces to clean
     * column names for processing and field mapping.
     */
    private String replaceSpecialCharacters(String input) {
        return input.replaceAll("[^a-zA-Z0-9]", " ");
    }

    /**
     * Converts input strings to camelCase format for consistent field naming.
     * Transforms space-separated words into camelCase convention where
     * the first word is lowercase and subsequent words are capitalized.
     */
    private String toCamelCase(String input) {
        StringBuilder camelCase = new StringBuilder();
        boolean capitalizeNext = false;

        for (char c : input.toCharArray()) {
            if (c == ' ') {
                capitalizeNext = true;
            } else {
                if (camelCase.length() == 0) {
                    camelCase.append(Character.toLowerCase(c));
                } else if (capitalizeNext) {
                    camelCase.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    camelCase.append(c);
                }
            }
        }

        return camelCase.toString();
    }

    private enum PartitionType {
        ROW, ID, EMPTY
    }
}
