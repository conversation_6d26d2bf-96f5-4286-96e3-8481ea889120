package org.apache.fineract.cn.originate.service.SpringBatch.Handler;

import com.centelon.finnate.workflowengine.api.v1.events.WorkflowEngineSampleEventConstants;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.fineract.cn.command.annotation.Aggregate;
import org.apache.fineract.cn.command.annotation.CommandHandler;
import org.apache.fineract.cn.command.annotation.CommandLogLevel;
import org.apache.fineract.cn.command.annotation.EventEmitter;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.SpringBatch.Command.UpdateComplexDealCsvBatchCommand;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.DealBatchDomain;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.validation.Valid;
import java.time.Clock;
import java.time.LocalDateTime;

/**
 * Command handler for complex deal CSV batch update operations.
 * Manages the execution of complex deal update batch jobs with advanced partitioning,
 * threading configuration, and parameter setup for sophisticated update workflows.
 */
@Aggregate
public class UpdateComplexDealCsvBatchHandler {
    private final JobRegistry jobRegistry;
    @Autowired
    @Qualifier("dealCsvBatchUpdateJob")
    private Job dealJob;
    @Autowired
    @Qualifier("asyncJobLauncher")
    private JobLauncher asyncJobLauncher;
    private JobExecution jobExecution;
    @Autowired
    private BatchUtils utils;

    /**
     * Constructor that initializes the UpdateComplexDealCsvBatchHandler with job registry.
     * Sets up the handler for managing complex deal update batch job execution
     * and provides access to registered batch jobs.
     */
    public UpdateComplexDealCsvBatchHandler(JobRegistry jobRegistry) {
        this.jobRegistry = jobRegistry;
    }

    /**
     * Main command handler method for processing complex deal update CSV batch commands.
     * Retrieves partition configuration, sets up threading parameters, configures job parameters
     * with advanced partitioning and threading settings, and launches the complex update batch job.
     */
    @CommandHandler(logStart = CommandLogLevel.INFO, logFinish = CommandLogLevel.INFO)
    @EventEmitter(selectorName = WorkflowEngineSampleEventConstants.SELECTOR_NAME, selectorValue = WorkflowEngineSampleEventConstants.BATCH)
    public Long dealBatchHandler(@Valid final UpdateComplexDealCsvBatchCommand dealCsvBatchCommand) throws Exception {
        DealBatchDomain domainData = dealCsvBatchCommand.getDealBatchDomain();
        String partitionKey = "";
        long gridSize;
        long corePoolSize = 0;
        long maxPoolSize = 0;
        long queueCapacity = 0;
        long throttleLimit = 0;
        long chunkSize = 0;
        String isThreadingRequired = "false";
        JsonNode partitionValues = utils.getPartitionValues(domainData.getBusinessProcessName(), "Update_deal_keys");
        if (partitionValues != null) {
            if (partitionValues.has("child_key")) {
                partitionKey = "";
            } else {
                partitionKey = "";
            }
            if (partitionValues.has("size")) {
                gridSize = partitionValues.get("size").asLong();
            } else {
                gridSize = 4L;
            }
            if (partitionValues.has("threadingParameters") && partitionValues.get("threadingRequired").asBoolean()) {
                isThreadingRequired = "true";
                JsonNode threadingParameters = partitionValues.get("threadingParameters");
                if (threadingParameters.get("taskExecutor").has("corePoolSize")) {
                    corePoolSize = threadingParameters.get("taskExecutor").get("corePoolSize").asLong();
                }
                if (threadingParameters.get("taskExecutor").has("maxPoolSize")) {
                    maxPoolSize = threadingParameters.get("taskExecutor").get("maxPoolSize").asLong();
                }
                if (threadingParameters.get("taskExecutor").has("queueCapacity")) {
                    queueCapacity = threadingParameters.get("taskExecutor").get("queueCapacity").asLong();
                }
                if (threadingParameters.get("taskExecutor").has("throttleLimit")) {
                    throttleLimit = threadingParameters.get("taskExecutor").get("throttleLimit").asLong();
                }
                if (threadingParameters.has("chunkSize")) {
                    chunkSize = threadingParameters.get("chunkSize").asLong();
                }

            } else {
                gridSize = 1L;
            }
        } else {
            partitionKey = "";
            gridSize = 1L;
        }
        JobParameters jobParameters = new JobParametersBuilder().addLong("time", System.currentTimeMillis())
                .addString("URL", domainData.getUrl())
                .addString("timeStamp", LocalDateTime.now(Clock.systemUTC()).toString())
                .addString("sheetName", domainData.getSheetName())
                .addString("createdBy", "FINNATE")
                .addString("partitionColumn", partitionKey)
                .addLong("gridSize", gridSize)
                .addLong("corePoolSize", corePoolSize)
                .addLong("maxPoolSize", maxPoolSize)
                .addLong("queueCapacity", queueCapacity)
                .addLong("throttleLimit", throttleLimit)
                .addLong("chunkSize", chunkSize)
                .addString("isThreadingRequired", isThreadingRequired)
                .addLong("businessProcessId", domainData.getBusinessProcessId())
                .toJobParameters();
        jobExecution = asyncJobLauncher.run(dealJob, jobParameters);
        return jobExecution.getJobId();

    }
}
