package org.apache.fineract.cn.originate.service.SpringBatch.Command;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.EntityBatchDomain;

/**
 * Command class for entity CSV batch processing operations.
 * Encapsulates the entity batch domain data required for executing
 * batch import and processing operations for entity objects.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EntityCsvBatchCommand {
    private EntityBatchDomain domain;
}
