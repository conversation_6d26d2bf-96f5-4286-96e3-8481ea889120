package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.stereotype.Component;

@Component
public class PartitionStepExecutionListener implements StepExecutionListener {

    private static final Logger logger = LoggerFactory.getLogger(PartitionStepExecutionListener.class);

    /**
     * Executes before each partition step begins to log partition initialization details.
     * Logs different information based on partition type (ID-based or row-based),
     * including partition identifiers and thread information for monitoring parallel execution.
     */
    @Override
    public void beforeStep(StepExecution stepExecution) {
        String partitionType = stepExecution.getExecutionContext().getString("partitionType", "UNKNOWN");
        String partitionColumn = stepExecution.getExecutionContext().getString("columnName", "UNKNOWN");

        if ("ID".equals(partitionType)) {
            String Id = stepExecution.getExecutionContext().getString("ID", "UNKNOWN");
            logger.info("Starting partition for {}: {} on thread: {}",
                    partitionColumn, Id, Thread.currentThread().getName());
        } else {
            logger.info("Starting partition: {} on thread: {}",
                    stepExecution.getStepName(), Thread.currentThread().getName());
        }
    }

    /**
     * Executes after each partition step completes to log execution statistics and results.
     * Provides detailed metrics including read, processed, written, filtered, and skipped counts,
     * with different logging formats for ID-based versus row-based partitions.
     */
    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        String partitionType = stepExecution.getExecutionContext().getString("partitionType", "UNKNOWN");

        if ("ID".equals(partitionType)) {
            String id = stepExecution.getExecutionContext().getString("ID", "UNKNOWN");
            logger.info("Completed partition for ScheduleId: {} on thread: {}. " +
                            "Read: {}, Processed: {}, Written: {}, Filtered: {}, Skipped: {}",
                    id,
                    Thread.currentThread().getName(),
                    stepExecution.getReadCount(),
                    stepExecution.getProcessSkipCount(),
                    stepExecution.getWriteCount(),
                    stepExecution.getFilterCount(),
                    stepExecution.getSkipCount());
        } else {
            logger.info("Completed partition: {} on thread: {}. " +
                            "Read: {}, Processed: {}, Written: {}, Filtered: {}, Skipped: {}",
                    stepExecution.getStepName(),
                    Thread.currentThread().getName(),
                    stepExecution.getReadCount(),
                    stepExecution.getProcessSkipCount(),
                    stepExecution.getWriteCount(),
                    stepExecution.getFilterCount(),
                    stepExecution.getSkipCount());
        }

        return stepExecution.getExitStatus();
    }
}