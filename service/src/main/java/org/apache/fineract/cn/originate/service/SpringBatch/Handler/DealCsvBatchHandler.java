package org.apache.fineract.cn.originate.service.SpringBatch.Handler;

import com.centelon.finnate.workflowengine.api.v1.events.WorkflowEngineSampleEventConstants;
import org.apache.fineract.cn.command.annotation.Aggregate;
import org.apache.fineract.cn.command.annotation.CommandHandler;
import org.apache.fineract.cn.command.annotation.CommandLogLevel;
import org.apache.fineract.cn.command.annotation.EventEmitter;
import org.apache.fineract.cn.originate.service.SpringBatch.Command.DealCsvBatchCommand;

import org.apache.fineract.cn.originate.service.internal.repository.SpringBatch.DealBatchDomain;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.validation.Valid;
import java.time.Clock;
import java.time.LocalDateTime;

/**
 * Command handler for deal CSV batch processing operations.
 * Manages the execution of deal batch jobs including parameter setup,
 * job launching, and execution tracking for deal creation workflows.
 */
@Aggregate
public class DealCsvBatchHandler {
    private final JobRegistry jobRegistry;
    @Autowired
    @Qualifier("dealCsvBatchInsertJob")
    private Job dealJob;
    @Autowired
    @Qualifier("asyncJobLauncher")
    private JobLauncher asyncJobLauncher;
    private JobExecution jobExecution;

    /**
     * Constructor that initializes the DealCsvBatchHandler with job registry.
     * Sets up the handler for managing deal batch job execution and
     * provides access to registered batch jobs.
     */
    public DealCsvBatchHandler(JobRegistry jobRegistry) {
        this.jobRegistry = jobRegistry;
    }

    /**
     * Main command handler method for processing deal CSV batch commands.
     * Extracts domain data, builds job parameters with URL, timestamps, and business process info,
     * launches the deal batch job asynchronously, and returns the job execution ID for tracking.
     */
    @CommandHandler(logStart = CommandLogLevel.INFO, logFinish = CommandLogLevel.INFO)
    @EventEmitter(selectorName = WorkflowEngineSampleEventConstants.SELECTOR_NAME, selectorValue = WorkflowEngineSampleEventConstants.BATCH)
    public Long dealBatchHandler(@Valid final DealCsvBatchCommand dealCsvBatchCommand) throws Exception {
        DealBatchDomain domainData = dealCsvBatchCommand.getDealBatchDomain();
        JobParameters jobParameters = new JobParametersBuilder().addLong("time", System.currentTimeMillis())
                .addString("URL", domainData.getUrl())
                .addString("timeStamp", LocalDateTime.now(Clock.systemUTC()).toString())
                .addString("sheetName", domainData.getSheetName())
                .addString("createdBy", "FINNATE")
                .addLong("businessProcessId", domainData.getBusinessProcessId())
                .toJobParameters();
        jobExecution = asyncJobLauncher.run(dealJob, jobParameters);
        return jobExecution.getJobId();

    }
}
