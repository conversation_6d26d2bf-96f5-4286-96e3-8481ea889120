package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

public class DealCsvJobCompletionNotificationListner implements JobExecutionListener {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    @Qualifier("dealCsvBatchInsertJob")
    private Job yourJob;

    /**
     * Executes before the deal CSV job starts.
     * Currently empty implementation but provides hook for pre-job
     * initialization or setup tasks if needed in the future.
     */
    @Override
    public void beforeJob(JobExecution jobExecution) {

    }

    /**
     * Executes after the deal CSV job completes to handle error recovery.
     * Automatically restarts failed or stopped jobs using the same parameters
     * to provide resilience and ensure job completion.
     */
    @Override
    public void afterJob(JobExecution jobExecution) {

        if (jobExecution.getStatus() == BatchStatus.FAILED || jobExecution.getStatus() == BatchStatus.STOPPED) {

            try {
                String jobName = jobExecution.getJobInstance().getJobName();
                JobExecution lastExecution = jobRepository.getLastJobExecution(jobName, jobExecution.getJobParameters());

                if (lastExecution != null) {
                    JobParameters lastJobParameters = lastExecution.getJobParameters();
                    jobLauncher.run(yourJob, lastJobParameters);
                }
            } catch (Exception e) {
                throw new RuntimeException("Error Occurred", e);
            }
        }
    }
}
