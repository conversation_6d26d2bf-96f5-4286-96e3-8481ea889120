package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

public class DealCsvJobCompletionNotificationListner implements JobExecutionListener {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    @Qualifier("dealCsvBatchInsertJob")
    private Job yourJob;

    @Override
    public void beforeJob(JobExecution jobExecution) {

    }

    @Override
    public void afterJob(JobExecution jobExecution) {

        if (jobExecution.getStatus() == BatchStatus.FAILED || jobExecution.getStatus() == BatchStatus.STOPPED) {

            try {
                String jobName = jobExecution.getJobInstance().getJobName();
                JobExecution lastExecution = jobRepository.getLastJobExecution(jobName, jobExecution.getJobParameters());

                if (lastExecution != null) {
                    JobParameters lastJobParameters = lastExecution.getJobParameters();
                    jobLauncher.run(yourJob, lastJobParameters);
                }
            } catch (Exception e) {
                throw new RuntimeException("Error Occurred", e);
            }
        }
    }
}
