package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;


import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.DealComposite;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealAssetRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealRepository;
import org.slf4j.Logger;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import javax.persistence.EntityManager;

import java.util.List;

public class UpdateDealItemWriter implements ItemWriter<DealComposite> {
    @Autowired
    private  DealRepository dealRepository;
    @Autowired
    private DealAssetRepository dealAssetRepository;
    @Autowired
    @Qualifier("originate-logger")
    private  Logger logger;

    private final String sheetName;
    private final String url;
    @Autowired
    private EntityManager entityManager;


    @Autowired
    public UpdateDealItemWriter(
            String sheetName,
            String url) throws Exception {
        this.sheetName = sheetName;
        this.url = url;
    }


    /**
     * Callback method executed before the step starts.
     *
     * @param stepExecution The StepExecution instance
     */
    @BeforeStep
    public void saveFilePath(StepExecution stepExecution) {
        // Implementation for pre-step processing if needed
    }

    /**
     * Writes the provided list of DealComposite objects to the repository.
     *
     * @param items List of DealComposite objects to be written
     * @throws Exception if writing fails
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW,
            rollbackFor = Exception.class)
    public void write(List<? extends DealComposite> items) throws Exception {
        logger.info("Inside writer");
        if (items.isEmpty()) {
            return;
        }
        try {
            logger.info("trying to save deal entity of size {}", items.size());
            for (DealComposite composite : items) {
                dealAssetRepository.save(composite.getDealAssetEntity());
                dealRepository.save(composite.getDealEntity());
                entityManager.flush();
                entityManager.clear();
            }
        } catch (Exception e) {
            logger.error("Failed to save deal entities: {}", e.getMessage(), e);
            throw e;
        }
    }
}
