package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.fineract.cn.originate.api.v1.domain.deal.Deal;
import org.apache.fineract.cn.originate.api.v1.domain.deal.DealAsset;
import org.apache.fineract.cn.originate.api.v1.validation.utils.Utility;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.DealComposite;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.DealRowMapper;
import org.apache.fineract.cn.originate.service.internal.mapper.DealMapper;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealAssetEntity;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealAssetRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealRepository;
import org.apache.fineract.cn.originate.service.internal.service.BusinessProcessEventService;
import org.apache.fineract.cn.originate.service.internal.service.DealService;
import org.apache.fineract.cn.originate.service.internal.service.externalservice.JsonParseUtility;
import org.slf4j.Logger;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;

import java.time.LocalDateTime;
import java.util.*;

public class UpdateDealItemProcessor implements ItemProcessor<JsonNode, DealComposite> {

    private static final String BUSINESS_PROCESS_DEFINITION = "businessProcessDefinition";
    private static final String IN_PROGRESS_STATUS = "In progress";
    private final ObjectMapper objectMapper;
    private final Long businessProcessId;
    @Autowired
    private BusinessProcessDefinitionRepository businessProcessDefinitionRepository;
    @Autowired
    private DealAssetRepository dealAssetRepository;
    @Autowired
    private JsonParseUtility jsonParseUtility;
    @Autowired
    private DealRepository dealRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private BusinessProcessEventService businessProcessEventService;
    @Autowired
    private BatchUtils batchUtils;
    @Autowired
    private DealService dealService;
    @Autowired
    @Qualifier("originate-logger")
    private Logger logger;
    private BusinessProcessDefinitionEntity cachedBusinessProcessDefinition;


    /**
     * Constructor that initializes the UpdateDealItemProcessor with a business process ID.
     * Sets up the processor for handling deal updates with JSON processing capabilities.
     */
    public UpdateDealItemProcessor(Long businessProcessId) {
        this.businessProcessId = businessProcessId;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Main processing method that updates existing deal entities based on JSON input.
     * Fetches deals using configured keys, merges new data with existing deal assets,
     * and returns a composite object containing updated deal and asset entities.
     */
    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW,
            rollbackFor = Exception.class)
    public DealComposite process(JsonNode node) throws Exception {
        DealEntity dealEntity = new DealEntity();
        DealAssetEntity dealAssetEntity = new DealAssetEntity();
        logger.info("Inside Deal Item Processor {}", node);
        BusinessProcessDefinitionEntity businessProcessDefinition = getBusinessProcessDefinition();
        ArrayList dealKey = batchUtils.getConfigValue(businessProcessDefinition.getName(), "Update_deal_keys");
        ArrayList<Deal> dealList = fetchDealsBasedOnKeysAndBusinessProcess(node, dealKey, (businessProcessDefinition.getId()));
        if (!dealList.isEmpty()) {
            for (Deal deal : dealList) {
                dealEntity = DealMapper.map(deal);
                dealEntity.setModifiedBy("FINNATE");
                dealEntity.setModifiedDate(LocalDateTime.now());
                dealEntity.setBusinessProcessDefinitionEntity(businessProcessDefinition);
                dealEntity.setCreatedBy(deal.getCreatedBy());
                String dateStr = deal.getCreatedDate();
                LocalDateTime parsedDate;
                try {
                    parsedDate = LocalDateTime.parse(dateStr,
                            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
                } catch (Exception e) {
                    String[] parts = dateStr.split("\\.");
                    String datePart = parts[0];
                    parsedDate = LocalDateTime.parse(datePart,
                            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    if (parts.length > 1) {
                        int nanos = Integer.parseInt(parts[1]) * (int) Math.pow(10, 9 - parts[1].length());
                        parsedDate = parsedDate.withNano(nanos);
                    }
                }
                dealEntity.setCreatedDate(parsedDate);
                Optional<DealEntity> dealData = this.dealRepository.findById(deal.getId());
                if (dealData.isPresent()) {
                    dealAssetEntity = this.dealAssetRepository.findById(dealData.get().getDealAssetEntity().getId());
                    JsonNode dealAsset = this.batchUtils.batchMerge(dealAssetEntity.getDealAsset(), node, businessProcessDefinition);
                    dealAssetEntity.setDealAsset(dealAsset);
                    dealAssetEntity.setModifiedBy("FINNATE");
                    dealAssetEntity.setModifiedDate(LocalDateTime.now());
                    dealEntity.setDealAssetEntity(dealAssetEntity);

                }
            }
            return new DealComposite(dealEntity, dealAssetEntity);
        } else {
            logger.info("No Deal Found for the given key {}", dealKey);
            return null;
        }


    }

    private ArrayList<Deal> fetchDealsBasedOnKeysAndBusinessProcess(JsonNode node, ArrayList<String> fieldKeys, Long businessProcessId) {
        ArrayList<Deal> dealList = new ArrayList<>();
        if (fieldKeys == null || fieldKeys.isEmpty()) {
            return dealList;
        }


        StringBuilder sqlBuilder = new StringBuilder(
                "SELECT * FROM originate_deal od JOIN originate_deal_asset oda ON oda.id = od.deal_asset_id " +
                        "WHERE od.business_process_id = ?");

        ArrayList<Object> params = new ArrayList<>();
        params.add(businessProcessId);


        for (String key : fieldKeys) {
            if (node.has(key)) {
                // TODO: structure the query builder Specific to type based on condition
                sqlBuilder.append(" AND (oda.deal_asset_obj ->> ? = ? OR oda.deal_asset_obj -> ? ->> 'name' = ?)");

                String valueToMatch;
                if (isComplexObject(node.get(key))) {
                    valueToMatch = node.get(key).get("value").get("name").asText();
                } else {
                    valueToMatch = node.get(key).asText();
                }
                params.add(key);
                params.add(valueToMatch);
                params.add(key);
                params.add(valueToMatch);
            } else {
                logger.info("Field key '{}' not found in node", key);
            }
        }


        if (params.size() > 1) {
            String sql = sqlBuilder.toString();
            logger.debug("Executing SQL: {} with params: {}", sql, params);
            dealList = (ArrayList<Deal>) jdbcTemplate.query(sql, new DealRowMapper<>(), params.toArray());
        }

        return dealList;
    }

    private boolean isComplexObject(JsonNode node) {
        return node.isObject() &&
                node.has("value") &&
                node.get("value").isObject() &&
                node.get("value").has("name");
    }


    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        if (cachedBusinessProcessDefinition != null) {
            return cachedBusinessProcessDefinition;
        }

        Object cachedDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get(BUSINESS_PROCESS_DEFINITION);

        if (cachedDefinition instanceof BusinessProcessDefinitionEntity) {
            cachedBusinessProcessDefinition = (BusinessProcessDefinitionEntity) cachedDefinition;
            return cachedBusinessProcessDefinition;
        }

        Optional<BusinessProcessDefinitionEntity> definitionEntity =
                businessProcessDefinitionRepository.findById(businessProcessId);

        if (!definitionEntity.isPresent()) {
            throw new IllegalArgumentException(
                    "Business Process definition not found for Id: " + businessProcessId);
        }

        cachedBusinessProcessDefinition = definitionEntity.get();
        putInJobExecutionContext(BUSINESS_PROCESS_DEFINITION, cachedBusinessProcessDefinition);
        return cachedBusinessProcessDefinition;
    }

    private void putInJobExecutionContext(String key, Object value) {
        ExecutionContext stepExecutionContext = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext();
        stepExecutionContext.put(key, value);
    }

}