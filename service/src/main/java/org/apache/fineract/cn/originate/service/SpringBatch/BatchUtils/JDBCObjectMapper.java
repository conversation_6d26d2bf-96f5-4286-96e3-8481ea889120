package org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

public class JDBCObjectMapper implements RowMapper<Object> {

    private static final Logger logger = LoggerFactory.getLogger(JDBCObjectMapper.class);

    @Override
    public Map<String, Object> mapRow(ResultSet rs, int rowNum) throws SQLException {
        Map<String, Object> reportData = new LinkedHashMap<>();
        int columnCount = rs.getMetaData().getColumnCount();

        IntStream.rangeClosed(1, columnCount)
                .forEach(i -> {
                    try {
                        reportData.put(rs.getMetaData().getColumnName(i), rs.getObject(i));
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }
                });

        return reportData;
    }
}
