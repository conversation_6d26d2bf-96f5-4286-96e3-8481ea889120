package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.support.Partitioner;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.io.IOException;
import java.util.*;

@Component
@StepScope
public class DealPartitioner implements Partitioner {

    private static final Logger logger = LoggerFactory.getLogger(DealPartitioner.class);

    @Autowired
    private BatchUtils batchUtils;

    @Value("#{jobParameters[URL]}")
    private String filePath;

    @Value("#{jobParameters[businessProcessId]}")
    private Long businessProcessId;

    @Value("#{jobParameters[partitionColumn]}")
    private String partitionColumn;
    @Autowired
    private BusinessProcessDefinitionRepository businessProcessDefinitionRepository;

    @Override
    public Map<String, ExecutionContext> partition(int gridSize) {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode configValues;
        JsonNode partitionValues = null;
        ArrayNode filterKey = mapper.createArrayNode();
        ArrayNode updateKey = mapper.createArrayNode();

        Optional<BusinessProcessDefinitionEntity> businessProcessDefinition = businessProcessDefinitionRepository.findById(businessProcessId);
        if (businessProcessDefinition.isPresent()) {
            configValues = batchUtils.getConfigValues(businessProcessDefinition.get().getName(), "Update_deal_keys");
            if (configValues != null) {
                if (configValues.has("filterChild")) {
                    filterKey = (ArrayNode) configValues.get("filterChild");
                }
                if (configValues.has("update")) {
                    updateKey = (ArrayNode) configValues.get("update");
                }
                if (configValues.has("partition")) {
                    partitionValues = configValues.get("partition");
                }
            }

        } else {
            throw new IllegalArgumentException("Business Process definition not found for Id: " + businessProcessId);

        }

        Map<String, ExecutionContext> result = new HashMap<>();

        try {
            try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
                List<String[]> allRows = reader.readAll();

                if (allRows.isEmpty()) {
                    logger.warn("CSV file is empty");
                    ExecutionContext context = new ExecutionContext();
                    context.putString("minValue", "0");
                    context.putString("maxValue", "0");
                    context.putString("partitionId", "partition0");
                    context.putLong("businessProcessId", businessProcessId);
                    result.put("partition0", context);
                    return result;
                }

                String[] headers = allRows.get(0);
                if (filterKey.size() > 0) {
                    assert partitionValues != null;
                    String key = partitionValues.get("child_key").asText();
                    if (isKeyPresentInHeaders(headers, filterKey.get(0).asText())) {
                        partitionColumn = key;
                    } else {
                        gridSize = partitionValues.get("update_size").asInt();
                    }
                } else {
                    gridSize = partitionValues.get("update_size").asInt();
                }


                int columnIndex = -1;
                for (int i = 0; i < headers.length; i++) {
                    if (headers[i].trim().equalsIgnoreCase(partitionColumn)) {
                        columnIndex = i;
                        break;
                    }
                }

                if (columnIndex == -1) {
                    logger.warn("Partition column '{}' not found in CSV headers, falling back to row-based partitioning", partitionColumn);
                    return createRowBasedPartitions(allRows, gridSize, result);
                }

                Map<String, List<Integer>> scheduleIdToRows = new HashMap<>();
                for (int i = 1; i < allRows.size(); i++) {
                    String[] row = allRows.get(i);
                    if (columnIndex < row.length) {
                        String scheduleId = row[columnIndex].trim();
                        if (!scheduleId.isEmpty()) {
                            scheduleIdToRows.computeIfAbsent(scheduleId, k -> new ArrayList<>()).add(i);
                        }
                    }
                }

                List<String> uniqueIds = new ArrayList<>(scheduleIdToRows.keySet());
                Collections.sort(uniqueIds);

                logger.info("Found {} unique ScheduleIds in the CSV file", uniqueIds.size());

                int partitionCount = 0;
                for (String Id : uniqueIds) {
                    List<Integer> rowIndices = scheduleIdToRows.get(Id);

                    ExecutionContext context = new ExecutionContext();
                    context.putString("partitionType", "ID");//ID, ROW, UNKNOWN
                    context.putString("ID", Id);
                    context.putString("columnName", partitionColumn);
                    context.putInt("columnIndex", columnIndex);
                    context.putString("partitionId", "partition" + partitionCount);
                    context.putLong("businessProcessId", businessProcessId);

                    StringBuilder rowIndicesStr = new StringBuilder();
                    for (int i = 0; i < rowIndices.size(); i++) {
                        if (i > 0) rowIndicesStr.append(",");
                        rowIndicesStr.append(rowIndices.get(i));
                    }
                    context.putString("rowIndices", rowIndicesStr.toString());
                    result.put("partition" + partitionCount, context);

                    logger.info("Created partition {} for ScheduleId {}: {} rows",
                            partitionCount, Id, rowIndices.size());

                    partitionCount++;
                }

                if (partitionCount == 0) {
                    ExecutionContext context = new ExecutionContext();
                    context.putString("partitionType", "EMPTY");
                    context.putString("partitionId", "partition0");
                    context.putLong("businessProcessId", businessProcessId);
                    result.put("partition0", context);
                }
            } catch (CsvException e) {
                throw new RuntimeException(e);
            }
        } catch (IOException e) {
            logger.error("Error partitioning deal data", e);
            throw new RuntimeException("Failed to partition deal data", e);
        }

        return result;
    }

    private Map<String, ExecutionContext> createRowBasedPartitions(List<String[]> allRows, int gridSize, Map<String, ExecutionContext> result) {
        int totalRecords = allRows.size() - 1;
        if (totalRecords <= 0) {
            logger.warn("CSV file contains only headers or is empty");
            ExecutionContext context = new ExecutionContext();
            context.putString("partitionType", "ROW");
            context.putString("minValue", "0");
            context.putString("maxValue", "0");
            context.putString("partitionId", "partition0");
            context.putLong("businessProcessId", businessProcessId);
            result.put("partition0", context);
            return result;
        }

        double recordsPerPartitionDouble = Math.ceil((double) totalRecords / gridSize);//3.0
        int recordsPerPartition = (int) recordsPerPartitionDouble;//3

        logger.info("Partitioning {} records into {} partitions with ~{} records per partition",
                totalRecords, gridSize, recordsPerPartition);

        for (int i = 0; i < gridSize && i * recordsPerPartition < totalRecords; i++) {
            ExecutionContext context = new ExecutionContext();

            int start = i * recordsPerPartition + 1; // +1 to skip header
            int end = Math.min(start + recordsPerPartition - 1, totalRecords);

            context.putString("partitionType", "ROW");
            context.putString("minValue", String.valueOf(start));
            context.putString("maxValue", String.valueOf(end));
            context.putString("partitionId", "partition" + i);
            context.putLong("businessProcessId", businessProcessId);

            result.put("partition" + i, context);

            logger.info("Created partition {}: records {} to {}", i, start, end);
        }

        return result;
    }

    private boolean isKeyPresentInHeaders(String[] headers, String key) {
        for (String header : headers) {
            if (header.trim().equalsIgnoreCase(key)) {
                return true;
            }
        }
        return false;
    }
}
