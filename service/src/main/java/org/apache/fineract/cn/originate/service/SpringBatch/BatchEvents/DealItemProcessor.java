package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.BusinessProcessEventEnum;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.WorkflowEngineResponse;
import org.apache.fineract.cn.originate.api.v1.domain.deal.BusinessProcessDetail;
import org.apache.fineract.cn.originate.api.v1.domain.deal.Deal;
import org.apache.fineract.cn.originate.api.v1.domain.deal.DealAsset;
import org.apache.fineract.cn.originate.api.v1.validation.utils.Utility;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealAssetRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;
import org.apache.fineract.cn.originate.service.internal.service.BusinessProcessEventService;
import org.apache.fineract.cn.originate.service.internal.service.DealService;
import org.slf4j.Logger;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.transformJson;

public class DealItemProcessor implements ItemProcessor<JsonNode, DealEntity> {

    private static final String BUSINESS_PROCESS_DEFINITION = "businessProcessDefinition";
    private static final String IN_PROGRESS_STATUS = "In progress";

    private final ObjectMapper objectMapper;
    private final Long businessProcessId;

    @Autowired
    private BusinessProcessDefinitionRepository businessProcessDefinitionRepository;

    @Autowired
    private DealAssetRepository dealAssetRepository;

    @Autowired
    private BusinessProcessEventService businessProcessEventService;

    @Autowired
    private BatchUtils batchUtils;

    @Autowired
    private DealService dealService;

    @Autowired
    @Qualifier("originate-logger")
    private Logger logger;

    public DealItemProcessor(Long businessProcessId) {
        this.businessProcessId = businessProcessId;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public DealEntity process(JsonNode node) throws Exception {
        logger.info("Inside Deal Item Processor {}", node);

        if (!(node instanceof ObjectNode)) {
            throw new IllegalArgumentException("Expected item to be an ObjectNode");
        }

        BusinessProcessDefinitionEntity businessProcessDefinition = getBusinessProcessDefinition();
        Deal deal = createInitialDeal(businessProcessDefinition);
        DealEntity processedDeal = processDealAsset(node, deal, businessProcessDefinition);

        logger.info("Completed Processing for the deal {}", processedDeal.getDealIdentifier());
        return processedDeal;
    }

    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        Object cachedDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get(BUSINESS_PROCESS_DEFINITION);

        if (cachedDefinition instanceof BusinessProcessDefinitionEntity) {
            return (BusinessProcessDefinitionEntity) cachedDefinition;
        }

        Optional<BusinessProcessDefinitionEntity> definitionEntity =
                businessProcessDefinitionRepository.findById(businessProcessId);

        if (!definitionEntity.isPresent()) {
            throw new IllegalArgumentException(
                    "Business Process definition not found for Id: " + businessProcessId);
        }

        BusinessProcessDefinitionEntity definition = definitionEntity.get();
        putInJobExecutionContext(BUSINESS_PROCESS_DEFINITION, definition);
        return definition;
    }

    private Deal createInitialDeal(BusinessProcessDefinitionEntity businessProcessDefinition) {
        Deal deal = new Deal();

        batchUtils.findFirstStageDefinition(businessProcessDefinition.getId())
                .ifPresent(stage -> deal.setCurrentStageName(stage.getName()));

        deal.setCurrentStatus(IN_PROGRESS_STATUS);
        deal.setDealCustomerList(new ArrayList<>());
        deal.setDealIdentifier(businessProcessDefinition.getName());
        deal.setBusinessProcessDetail(createBusinessProcessDetail(businessProcessDefinition));
        deal.setDealAsset(createEmptyDealAsset());

        return deal;
    }

    private BusinessProcessDetail createBusinessProcessDetail(
            BusinessProcessDefinitionEntity businessProcessDefinition) {
        BusinessProcessDetail detail = new BusinessProcessDetail();
        detail.setAssetTypeName(
                businessProcessDefinition.getAssetTypeDefinitionEntity().getAssetTypeName());
        detail.setName(businessProcessDefinition.getName());
        detail.setVersion(1);
        return detail;
    }

    private DealAsset createEmptyDealAsset() {
        DealAsset dealAsset = new DealAsset();
        dealAsset.setDealAssetItem(objectMapper.createObjectNode());
        return dealAsset;
    }

    private DealEntity processDealAsset(JsonNode node, Deal deal,
                                        BusinessProcessDefinitionEntity businessProcessDefinition) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, JsonNode> map = new HashMap<>();

        ObjectNode outputJson = transformJson(node, mapper, "dealDetails");
        Utility.convertJsonNodeToMap(outputJson.get("dealDetails"), map, true);
        JsonNode dealDomainAssetItem = mapper.valueToTree(map);

        JsonNode mergedAssetItem = BusinessProcessEventService.merge(
                businessProcessDefinition.getAssetItems(), dealDomainAssetItem);
        deal.getDealAsset().setDealAssetItem(mergedAssetItem);

//        WorkflowEngineResponse response = executeWorkflowRules(deal);
//        validateWorkflowResponse(response);

        return dealService.dealSave(deal, null, businessProcessDefinition,
                dealDomainAssetItem, true);
    }

    private WorkflowEngineResponse executeWorkflowRules(Deal deal) {
        return businessProcessEventService.executeOnEvent(
                deal,
                BusinessProcessEventEnum.CREATE.getEvent(),
                null,
                null
        );
    }

    private void validateWorkflowResponse(WorkflowEngineResponse response) {
        if (response.getErrorList() != null && Boolean.TRUE.equals(response.getIsErrorpresent())) {
            throw new IllegalArgumentException(
                    "Error occurred while executing workflow rule: " +
                            response.getErrorList().toString());
        }
    }

    private void putInJobExecutionContext(String key, Object value) {
        ExecutionContext stepExecutionContext = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext();
        stepExecutionContext.put(key, value);
    }
}