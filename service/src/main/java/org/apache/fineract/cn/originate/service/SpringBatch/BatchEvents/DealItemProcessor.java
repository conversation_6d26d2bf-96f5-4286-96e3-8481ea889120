package org.apache.fineract.cn.originate.service.SpringBatch.BatchEvents;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.BusinessProcessEventEnum;
import org.apache.fineract.cn.originate.api.v1.domain.businessProcess.WorkflowEngineResponse;
import org.apache.fineract.cn.originate.api.v1.domain.deal.BusinessProcessDetail;
import org.apache.fineract.cn.originate.api.v1.domain.deal.Deal;
import org.apache.fineract.cn.originate.api.v1.domain.deal.DealAsset;
import org.apache.fineract.cn.originate.api.v1.validation.utils.Utility;
import org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionEntity;
import org.apache.fineract.cn.originate.service.internal.repository.businessProcess.BusinessProcessDefinitionRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealAssetRepository;
import org.apache.fineract.cn.originate.service.internal.repository.deal.DealEntity;
import org.apache.fineract.cn.originate.service.internal.service.BusinessProcessEventService;
import org.apache.fineract.cn.originate.service.internal.service.DealService;
import org.slf4j.Logger;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.apache.fineract.cn.originate.service.SpringBatch.BatchUtils.BatchUtils.transformJson;

public class DealItemProcessor implements ItemProcessor<JsonNode, DealEntity> {

    private static final String BUSINESS_PROCESS_DEFINITION = "businessProcessDefinition";
    private static final String IN_PROGRESS_STATUS = "In progress";

    private final ObjectMapper objectMapper;
    private final Long businessProcessId;

    @Autowired
    private BusinessProcessDefinitionRepository businessProcessDefinitionRepository;

    @Autowired
    private DealAssetRepository dealAssetRepository;

    @Autowired
    private BusinessProcessEventService businessProcessEventService;

    @Autowired
    private BatchUtils batchUtils;

    @Autowired
    private DealService dealService;

    @Autowired
    @Qualifier("originate-logger")
    private Logger logger;

    /**
     * Constructor that initializes the DealItemProcessor with a business process ID.
     * Sets up the ObjectMapper for JSON processing.
     */
    public DealItemProcessor(Long businessProcessId) {
        this.businessProcessId = businessProcessId;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Main processing method that transforms a JsonNode into a DealEntity.
     * This method orchestrates the entire deal processing workflow including
     * business process definition retrieval, deal creation, and asset processing.
     */
    @Override
    public DealEntity process(JsonNode node) throws Exception {
        logger.info("Inside Deal Item Processor {}", node);

        if (!(node instanceof ObjectNode)) {
            throw new IllegalArgumentException("Expected item to be an ObjectNode");
        }

        BusinessProcessDefinitionEntity businessProcessDefinition = getBusinessProcessDefinition();
        Deal deal = createInitialDeal(businessProcessDefinition);
        DealEntity processedDeal = processDealAsset(node, deal, businessProcessDefinition);

        logger.info("Completed Processing for the deal {}", processedDeal.getDealIdentifier());
        return processedDeal;
    }

    /**
     * Retrieves the business process definition entity either from cache or database.
     * First checks the job execution context for a cached version, otherwise
     * fetches from the repository and caches it for subsequent use.
     */
    private BusinessProcessDefinitionEntity getBusinessProcessDefinition() {
        Object cachedDefinition = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext()
                .get(BUSINESS_PROCESS_DEFINITION);

        if (cachedDefinition instanceof BusinessProcessDefinitionEntity) {
            return (BusinessProcessDefinitionEntity) cachedDefinition;
        }

        Optional<BusinessProcessDefinitionEntity> definitionEntity =
                businessProcessDefinitionRepository.findById(businessProcessId);

        if (!definitionEntity.isPresent()) {
            throw new IllegalArgumentException(
                    "Business Process definition not found for Id: " + businessProcessId);
        }

        BusinessProcessDefinitionEntity definition = definitionEntity.get();
        putInJobExecutionContext(BUSINESS_PROCESS_DEFINITION, definition);
        return definition;
    }

    /**
     * Creates an initial Deal object with default values and configuration.
     * Sets up the deal with the first stage, in-progress status, and
     * initializes collections and business process details.
     */
    private Deal createInitialDeal(BusinessProcessDefinitionEntity businessProcessDefinition) {
        Deal deal = new Deal();

        batchUtils.findFirstStageDefinition(businessProcessDefinition.getId())
                .ifPresent(stage -> deal.setCurrentStageName(stage.getName()));

        deal.setCurrentStatus(IN_PROGRESS_STATUS);
        deal.setDealCustomerList(new ArrayList<>());
        deal.setDealIdentifier(businessProcessDefinition.getName());
        deal.setBusinessProcessDetail(createBusinessProcessDetail(businessProcessDefinition));
        deal.setDealAsset(createEmptyDealAsset());

        return deal;
    }

    /**
     * Creates a BusinessProcessDetail object from the business process definition.
     * Extracts asset type name, process name, and sets the version to 1.
     */
    private BusinessProcessDetail createBusinessProcessDetail(
            BusinessProcessDefinitionEntity businessProcessDefinition) {
        BusinessProcessDetail detail = new BusinessProcessDetail();
        detail.setAssetTypeName(
                businessProcessDefinition.getAssetTypeDefinitionEntity().getAssetTypeName());
        detail.setName(businessProcessDefinition.getName());
        detail.setVersion(1);
        return detail;
    }

    /**
     * Creates an empty DealAsset object with an initialized ObjectNode.
     * This serves as the base structure for deal asset information.
     */
    private DealAsset createEmptyDealAsset() {
        DealAsset dealAsset = new DealAsset();
        dealAsset.setDealAssetItem(objectMapper.createObjectNode());
        return dealAsset;
    }

    /**
     * Processes the deal asset by transforming JSON data and merging it with business process asset items.
     * Converts the input JsonNode to deal details, merges with existing asset items,
     * and saves the deal through the deal service.
     */
    private DealEntity processDealAsset(JsonNode node, Deal deal,
                                        BusinessProcessDefinitionEntity businessProcessDefinition) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, JsonNode> map = new HashMap<>();

        ObjectNode outputJson = transformJson(node, mapper, "dealDetails");
        Utility.convertJsonNodeToMap(outputJson.get("dealDetails"), map, true);
        JsonNode dealDomainAssetItem = mapper.valueToTree(map);

        JsonNode mergedAssetItem = BusinessProcessEventService.merge(
                businessProcessDefinition.getAssetItems(), dealDomainAssetItem);
        deal.getDealAsset().setDealAssetItem(mergedAssetItem);

        WorkflowEngineResponse response = executeWorkflowRules(deal);
        validateWorkflowResponse(response);

        return dealService.dealSave(deal, null, businessProcessDefinition,
                dealDomainAssetItem, true);
    }

    /**
     * Executes workflow rules for the given deal using the CREATE event.
     * This method triggers the business process event service to run
     * workflow rules associated with deal creation.
     */
    private WorkflowEngineResponse executeWorkflowRules(Deal deal) {
        return businessProcessEventService.executeOnEvent(
                deal,
                BusinessProcessEventEnum.CREATE.getEvent(),
                null,
                null
        );
    }

    /**
     * Validates the workflow engine response for errors.
     * Throws an IllegalArgumentException if errors are present in the response,
     * preventing further processing of invalid workflow results.
     */
    private void validateWorkflowResponse(WorkflowEngineResponse response) {
        if (response.getErrorList() != null && Boolean.TRUE.equals(response.getIsErrorpresent())) {
            throw new IllegalArgumentException(
                    "Error occurred while executing workflow rule: " +
                            response.getErrorList().toString());
        }
    }

    /**
     * Stores a key-value pair in the job execution context for caching purposes.
     * This allows sharing data across different steps and items within the same batch job,
     * improving performance by avoiding repeated database lookups.
     */
    private void putInJobExecutionContext(String key, Object value) {
        ExecutionContext stepExecutionContext = StepSynchronizationManager.getContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext();
        stepExecutionContext.put(key, value);
    }
}